# Architecture des Données - Backend Strapi PillarScan

## 1. Vue d'ensemble de l'architecture

### 1.1 Philosophie de conception

L'architecture des données suit le principe de **"Single Source of Truth"** (source unique de vérité) où chaque information a un seul endroit de stockage, évitant ainsi les duplications et incohérences.

### 1.2 Schéma conceptuel global

```
┌─────────────────────────────────────────────────────────────┐
│                     ÉCOSYSTÈME PILLARSCAN                     │
├───────────────────┬─────────────────┬───────────────────────┤
│   UTILISATEURS    │   EXPRESSIONS   │   SYSTÈME PILIERS    │
│   • Profils       │   • Contenu      │   • 12 Piliers       │
│   • Rôles         │   • Médias       │   • 72 Sous-piliers  │
│   • Périmètres    │   • Statuts      │   • Classifications  │
├───────────────────┼─────────────────┼───────────────────────┤
│   MODÉRATION      │   ENTITÉS       │   GÉOLOCALISATION    │
│   • Validateurs   │   • Personnes    │   • Lieux            │
│   • Décisions     │   • Organisations│   • Zones            │
│   • Historique    │   • Relations    │   • Coordonnées      │
└───────────────────┴─────────────────┴───────────────────────┘
```

## 2. Les entités principales (Content Types)

### 2.1 Diagramme des relations

```
                    ┌──────────────┐
                    │   Profile    │
                    │ (Utilisateur)│
                    └──────┬───────┘
                           │ 1
                           │
                           │ *
                    ┌──────▼───────┐         1  ┌──────────────┐
                    │  Expression  │◄────────────│  Validateur  │
                    │              │             │              │
                    └──┬───┬───┬──┘             └──────────────┘
                       │   │   │
           ┌───────────┘   │   └───────────┐
           │ *             │ *             │ *
           ▼               ▼               ▼
    ┌──────────┐    ┌──────────┐   ┌──────────┐
    │  Média   │    │  Entité  │   │   Lieu   │
    └──────────┘    └──────────┘   └──────────┘
                           │
                           ▼ *
                    ┌──────────┐    ┌──────────┐
                    │  Pilier  │    │   Tag    │
                    └──────────┘    └──────────┘
```

### 2.2 Description détaillée des entités

#### 2.2.1 Profile (Profil Utilisateur)

| Champ                  | Type        | Description        | Exemple                 |
| ---------------------- | ----------- | ------------------ | ----------------------- |
| **email**              | Email       | Identifiant unique | <EMAIL>       |
| **nom**                | Texte       | Nom complet        | Marie Dupont            |
| **telephone**          | Texte       | Numéro de contact  | +33612345678            |
| **date_naissance**     | Date        | Pour statistiques  | 1985-03-15              |
| **genre**              | Énumération | M/F/Autre          | F                       |
| **lieu_residence**     | Relation    | Lien vers Lieu     | → Lieu#123              |
| **validateur_assigne** | Relation    | Son validateur     | → Validateur#45         |
| **role**               | Énumération | Type d'utilisateur | contributeur            |
| **statut**             | Énumération | Actif/Suspendu     | actif                   |
| **preferences**        | JSON        | Paramètres perso   | {"notifications": true} |
| **date_inscription**   | DateTime    | Inscription        | 2024-01-15 10:30:00     |

#### 2.2.2 Expression

| Champ                | Type              | Description                 | Exemple                             |
| -------------------- | ----------------- | --------------------------- | ----------------------------------- |
| **titre**            | Texte             | Généré ou manuel            | "Manque de médicaments à l'hôpital" |
| **contenu**          | Texte Long        | Message principal           | "Depuis 3 mois, l'hôpital..."       |
| **type_expression**  | Énumération       | Problème/Satisfaction/Idée  | probleme                            |
| **urgence**          | Nombre            | 1-5                         | 4                                   |
| **etat_emotionnel**  | Énumération       | Colère/Joie/Tristesse...    | colere                              |
| **statut**           | Énumération       | Brouillon/En attente/Publié | brouillon                           |
| **auteur**           | Relation          | Lien vers Profile           | → Profile#789                       |
| **validateur**       | Relation          | Qui a validé                | → Validateur#45                     |
| **date_evenement**   | Date              | Quand c'est arrivé          | 2024-11-20                          |
| **date_creation**    | DateTime          | Création                    | 2024-11-22 14:00:00                 |
| **date_soumission**  | DateTime          | Soumis à validation         | 2024-11-22 15:30:00                 |
| **date_publication** | DateTime          | Rendu public                | 2024-11-23 09:00:00                 |
| **medias**           | Relation Multiple | Fichiers joints             | → [Media#1, Media#2]                |
| **entites**          | Relation Multiple | Qui est concerné            | → [Entite#34, Entite#67]            |
| **lieu**             | Relation          | Où ça se passe              | → Lieu#890                          |
| **piliers**          | Relation Multiple | Classification              | → [Pilier#3, Pilier#7]              |
| **score_ia**         | JSON              | Résultats classification    | {"sante": 0.85, "education": 0.15}  |
| **metadata**         | JSON              | Données additionnelles      | {"source": "mobile", "version": 2}  |

#### 2.2.3 Validateur

| Champ            | Type              | Description           | Exemple                    |
| ---------------- | ----------------- | --------------------- | -------------------------- |
| **profile**      | Relation          | Lien vers utilisateur | → Profile#234              |
| **perimetres**   | Relation Multiple | Zones couvertes       | → [Perimetre#1, #2]        |
| **specialites**  | Relation Multiple | Piliers experts       | → [Pilier#3, #5]           |
| **niveau**       | Énumération       | Junior/Senior/Expert  | senior                     |
| **quota_jour**   | Nombre            | Max expressions/jour  | 50                         |
| **statistiques** | JSON              | Métriques performance | {"taux_approbation": 0.82} |
| **actif**        | Booléen           | En service ou non     | true                       |

#### 2.2.4 Entité

| Champ                | Type        | Description                  | Exemple                             |
| -------------------- | ----------- | ---------------------------- | ----------------------------------- |
| **nom**              | Texte       | Nom officiel                 | Hôpital Central                     |
| **type**             | Énumération | Personne/Organisation/Groupe | organisation                        |
| **sous_type**        | Texte       | Catégorie précise            | etablissement_sante                 |
| **description**      | Texte Long  | Détails                      | "Principal hôpital de la région..." |
| **identifiants**     | JSON        | IDs officiels                | {"siret": "123456789"}              |
| **contacts**         | JSON        | Coordonnées                  | {"tel": "0123456789"}               |
| **lieu_principal**   | Relation    | Adresse principale           | → Lieu#456                          |
| **responsable**      | Relation    | Contact principal            | → Profile#789                       |
| **verifie**          | Booléen     | Validé officiellement        | true                                |
| **reputation_score** | Nombre      | Score calculé                | 7.5                                 |

#### 2.2.5 Lieu

| Champ                | Type        | Description                | Exemple                                 |
| -------------------- | ----------- | -------------------------- | --------------------------------------- |
| **nom**              | Texte       | Nom du lieu                | Place de la République                  |
| **type**             | Énumération | Adresse/Zone/Point         | adresse                                 |
| **niveau**           | Énumération | Pays/Region/Ville/Quartier | ville                                   |
| **coordonnees**      | JSON        | GPS                        | {"lat": 48.8566, "lng": 2.3522}         |
| **adresse_complete** | Texte       | Adresse postale            | "1 Place de la République, 75003 Paris" |
| **code_postal**      | Texte       | Code postal                | 75003                                   |
| **ville**            | Texte       | Nom ville                  | Paris                                   |
| **region**           | Texte       | Région/État                | Île-de-France                           |
| **pays**             | Texte       | Code pays                  | FR                                      |
| **parent**           | Relation    | Lieu englobant             | → Lieu#123                              |
| **metadata**         | JSON        | Infos additionnelles       | {"population": 2200000}                 |

#### 2.2.6 Média

| Champ             | Type        | Description                | Exemple                         |
| ----------------- | ----------- | -------------------------- | ------------------------------- |
| **fichier**       | Media       | Fichier uploadé            | photo_123.jpg                   |
| **type**          | Énumération | Photo/Video/Audio/Document | photo                           |
| **titre**         | Texte       | Description                | "Photo de la file d'attente"    |
| **legende**       | Texte       | Contexte                   | "Prise le matin du 20/11"       |
| **taille**        | Nombre      | Taille en octets           | 2048576                         |
| **dimensions**    | JSON        | Si image/video             | {"width": 1920, "height": 1080} |
| **duree**         | Nombre      | Si video/audio (secondes)  | 120                             |
| **metadata_exif** | JSON        | Données EXIF               | {"camera": "iPhone 12"}         |
| **hash**          | Texte       | Empreinte unique           | "a4b5c6d7e8f9..."               |

#### 2.2.7 Pilier

| Champ            | Type              | Description          | Exemple                  |
| ---------------- | ----------------- | -------------------- | ------------------------ |
| **code**         | Texte             | Identifiant unique   | EDU                      |
| **nom**          | Texte             | Nom français         | Éducation                |
| **nom_en**       | Texte             | Nom anglais          | Education                |
| **description**  | Texte Long        | Explication          | "L'éducation englobe..." |
| **domaine**      | Énumération       | Catégorie principale | education_competences    |
| **ordre**        | Nombre            | Position affichage   | 1                        |
| **icone**        | Texte             | Nom icône            | graduation-cap           |
| **couleur**      | Texte             | Code couleur         | #3B82F6                  |
| **sous_piliers** | Relation Multiple | Détails              | → [SousPilier#1-6]       |
| **actif**        | Booléen           | Utilisé ou non       | true                     |

## 3. Les relations entre entités

### 3.1 Relations One-to-Many (1:N)

```
┌────────────────────────────────────────────────┐
│  Un Profile peut avoir plusieurs Expressions   │
│  Une Expression appartient à un seul Profile   │
├────────────────────────────────────────────────┤
│  Profile ────1:N───► Expression                │
│          expressions                            │
└────────────────────────────────────────────────┘

┌────────────────────────────────────────────────┐
│  Un Validateur suit plusieurs Profiles         │
│  Un Profile a un seul Validateur assigné       │
├────────────────────────────────────────────────┤
│  Validateur ────1:N───► Profile                │
│             profiles_suivis                     │
└────────────────────────────────────────────────┘
```

### 3.2 Relations Many-to-Many (N:N)

```
┌────────────────────────────────────────────────┐
│  Une Expression peut mentionner plusieurs      │
│  Entités, une Entité peut être dans plusieurs  │
│  Expressions                                    │
├────────────────────────────────────────────────┤
│  Expression ◄───N:N───► Entité                 │
│             entites_concernees                  │
└────────────────────────────────────────────────┘

┌────────────────────────────────────────────────┐
│  Une Expression peut avoir plusieurs Médias    │
│  Un Média peut être réutilisé (économie)       │
├────────────────────────────────────────────────┤
│  Expression ◄───N:N───► Média                  │
│             medias_joints                       │
└────────────────────────────────────────────────┘
```

### 3.3 Table de jointure enrichie

Pour certaines relations N:N, on stocke des informations supplémentaires :

```
┌─────────────────────────────────────────────────┐
│        ExpressionEntiteRelation                 │
├─────────────────────────────────────────────────┤
│  expression_id : Référence vers Expression      │
│  entite_id : Référence vers Entité             │
│  role : Énumération (principal/secondaire/...)  │
│  sentiment : Énumération (positif/négatif/...)  │
│  date_ajout : DateTime                          │
└─────────────────────────────────────────────────┘
```

## 4. Stratégies d'optimisation

### 4.1 Index et performances

```
┌─────────────────────────────────────────────────┐
│              INDEX CRITIQUES                    │
├─────────────────────────────────────────────────┤
│  expressions.statut + date_creation (composé)   │
│  expressions.auteur_id                          │
│  expressions.validateur_id + statut             │
│  entites.nom (recherche textuelle)              │
│  lieux.coordonnees (spatial)                    │
│  medias.hash (unicité)                          │
└─────────────────────────────────────────────────┘
```

### 4.2 Dénormalisation calculée

Certains champs sont calculés et stockés pour éviter des requêtes complexes :

| Entité     | Champ dénormalisé | Calcul                       | Mise à jour                 |
| ---------- | ----------------- | ---------------------------- | --------------------------- |
| Profile    | nb_expressions    | COUNT(expressions)           | Trigger après INSERT        |
| Profile    | derniere_activite | MAX(expressions.date)        | Trigger après INSERT/UPDATE |
| Entite     | reputation_score  | AVG(sentiments)              | Job quotidien               |
| Validateur | taux_approbation  | COUNT(approuvé)/COUNT(total) | Temps réel                  |

### 4.3 Archivage et historique

```
┌─────────────────────────────────────────────────┐
│           STRATÉGIE D'ARCHIVAGE                 │
├─────────────────────────────────────────────────┤
│  Actif (0-6 mois)    : Base principale          │
│  Archive (6-24 mois) : Base secondaire          │
│  Historique (>24m)   : Cold storage             │
│                                                 │
│  Accès :                                        │
│  • Actif : Temps réel                          │
│  • Archive : < 5 secondes                       │
│  • Historique : Sur demande                     │
└─────────────────────────────────────────────────┘
```

## 5. Sécurité et intégrité des données

### 5.1 Contraintes d'intégrité

```sql
-- Exemples de règles métier implémentées
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
• Une expression ne peut pas être validée par son auteur
• Un validateur ne peut pas modérer ses propres soumissions
• Une expression publiée ne peut plus être modifiée
• Un média orphelin est supprimé après 7 jours
• Un lieu doit avoir des coordonnées OU un parent
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
```

### 5.2 Audit trail (piste d'audit)

Chaque modification critique est tracée :

```
┌─────────────────────────────────────────────────┐
│              AuditLog                           │
├─────────────────────────────────────────────────┤
│  id : UUID                                      │
│  entite_type : String (Expression, Profile...)  │
│  entite_id : UUID                               │
│  action : Énumération (CREATE/UPDATE/DELETE)    │
│  champs_modifies : JSON                         │
│  valeur_avant : JSON                            │
│  valeur_apres : JSON                            │
│  utilisateur_id : UUID                          │
│  ip_address : String                            │
│  user_agent : String                            │
│  timestamp : DateTime                           │
└─────────────────────────────────────────────────┘
```

## 6. Gestion des médias

### 6.1 Stockage physique

```
┌─────────────────────────────────────────────────┐
│           ARCHITECTURE STOCKAGE                 │
├─────────────────────────────────────────────────┤
│                                                 │
│  Upload ──► Validation ──► Optimisation        │
│                │               │                 │
│                ▼               ▼                 │
│           Antivirus      Redimensionnement      │
│                │               │                 │
│                ▼               ▼                 │
│            Stockage :     Versions :            │
│            • Original    • Thumbnail (150x150)  │
│            • Optimisé    • Small (600x600)      │
│                         • Medium (1200x1200)    │
│                                                 │
│  Structure dossiers :                           │
│  /medias/{année}/{mois}/{jour}/{uuid}/         │
└─────────────────────────────────────────────────┘
```

### 6.2 CDN et cache

Les médias sont servis via CDN avec cache intelligent :

- Images : Cache 1 an (immutable)
- Thumbnails : Génération à la volée, cache 1 mois
- Purge automatique des médias non référencés

## 7. Classification et Intelligence Artificielle

### 7.1 Pipeline de classification

```
                 ┌─────────────────┐
                 │   Expression    │
                 │    soumise      │
                 └────────┬────────┘
                          │
                 ┌────────▼────────┐
                 │  Prétraitement  │
                 │ • Nettoyage     │
                 │ • Tokenisation  │
                 └────────┬────────┘
                          │
                 ┌────────▼────────┐
                 │  Analyse IA     │
                 │ • NLP           │
                 │ • Classification│
                 └────────┬────────┘
                          │
                 ┌────────▼────────┐
                 │  Enrichissement │
                 │ • Piliers       │
                 │ • Sentiments    │
                 │ • Entités       │
                 └────────┬────────┘
                          │
                 ┌────────▼────────┐
                 │   Stockage      │
                 │  score_ia       │
                 └─────────────────┘
```

### 7.2 Structure des résultats IA

```json
{
    "classification": {
        "piliers": [
            { "code": "SANTE", "score": 0.85, "confiance": "haute" },
            { "code": "EDUCATION", "score": 0.15, "confiance": "basse" }
        ],
        "sous_piliers": [
            { "code": "SANTE_ACCESS", "score": 0.75 },
            { "code": "SANTE_QUALITE", "score": 0.6 }
        ]
    },
    "analyse_sentiment": {
        "global": "negatif",
        "score": -0.7,
        "emotions": {
            "colere": 0.6,
            "frustration": 0.8,
            "tristesse": 0.3
        }
    },
    "entites_detectees": [
        {
            "texte": "Hôpital Central",
            "type": "organisation",
            "confiance": 0.95,
            "position": [15, 30]
        }
    ],
    "mots_cles": ["medicaments", "rupture", "urgence", "attente"],
    "titre_suggere": "Rupture de médicaments à l'Hôpital Central",
    "resume": "L'utilisateur signale une rupture de stock de médicaments essentiels..."
}
```

## 8. Évolutivité et scalabilité

### 8.1 Partitionnement des données

```
┌─────────────────────────────────────────────────┐
│         STRATÉGIE DE PARTITIONNEMENT            │
├─────────────────────────────────────────────────┤
│                                                 │
│  Expressions : Par date (mensuel)               │
│  ├─ expressions_2024_01                         │
│  ├─ expressions_2024_02                         │
│  └─ expressions_2024_03                         │
│                                                 │
│  Médias : Par type et taille                    │
│  ├─ medias_photos_small                         │
│  ├─ medias_photos_large                         │
│  └─ medias_videos                               │
│                                                 │
│  Lieux : Par niveau géographique                │
│  ├─ lieux_pays                                  │
│  ├─ lieux_regions                               │
│  └─ lieux_villes                                │
└─────────────────────────────────────────────────┘
```

### 8.2 Cache multiniveau

1. **Cache applicatif** (Redis) : Sessions, données chaudes
2. **Cache requêtes** (Strapi) : Résultats API fréquents
3. **Cache CDN** : Médias et contenus statiques
4. **Cache navigateur** : Assets et données utilisateur

## 9. Migration et compatibilité

### 9.1 Versioning des schémas

Chaque entité maintient sa version :

```json
{
    "_version": "1.2.0",
    "_migrated": "2024-11-25",
    "_compatible": ["1.0.0", "1.1.0", "1.2.0"]
}
```

### 9.2 Stratégie de migration

- Migrations réversibles
- Tests sur copie de production
- Déploiement progressif (canary)
- Rollback automatique si erreur

## 10. Monitoring et métriques

### 10.1 KPIs techniques

```
┌─────────────────────────────────────────────────┐
│            TABLEAU DE BORD DBA                  │
├─────────────────────────────────────────────────┤
│  Taille DB : 125 GB (↑ 2.3%/mois)              │
│  Requêtes/sec : 1,250 (pic : 3,400)            │
│  Latence P95 : 45ms                            │
│  Cache hit ratio : 87%                         │
│  Index usage : 94%                             │
│  Slow queries : 12/jour                        │
│                                                 │
│  Top tables par taille :                        │
│  • expressions : 45 GB                          │
│  • medias : 38 GB                               │
│  • audit_logs : 22 GB                           │
└─────────────────────────────────────────────────┘
```

Cette architecture garantit performance, sécurité et évolutivité pour supporter des millions d'utilisateurs et des milliards d'expressions.
