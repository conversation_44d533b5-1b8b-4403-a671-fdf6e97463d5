# Processus de Classification par Intelligence Artificielle - PillarScan

## 1. Vue d'ensemble du système

### 1.1 Objectif de la classification IA

Le système de classification IA fonctionne comme un **bibliothécaire ultra-rapide** qui :

- **Lit** et comprend chaque expression en quelques secondes
- **Analyse** le contenu pour en extraire le sens profond
- **Classe** l'expression dans les bons piliers et sous-piliers
- **Enrichit** avec des métadonnées utiles (sentiment, urgence, mots-clés)
- **Suggère** un titre accrocheur et pertinent

### 1.2 Architecture du pipeline IA

```
┌─────────────────────────────────────────────────────────────┐
│                    PIPELINE DE CLASSIFICATION                │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│   Expression      Pré-         Analyse      Post-          │
│   Brute     ───► traitement ──► IA      ──► traitement ──► │
│                                                             │
│   📝              🧹             🧠           ✨            │
│   Texte +         Nettoyer      Comprendre   Enrichir      │
│   Métadonnées     Normaliser    Classifier   Formater      │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

## 2. Étape 1 : Prétraitement

### 2.1 Objectifs du prétraitement

Préparer le texte pour une analyse optimale, comme préparer des ingrédients avant la cuisson.

### 2.2 Processus détaillé

```
┌─────────────────────────────────────────────────────────────┐
│                      PRÉTRAITEMENT                          │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  Texte original :                                           │
│  "Bjr, l'hôpital X à Yaoundé n'a plus de médicaments!!!   │
│   C'est INADMISSIBLE... Les malades souffrent 😢"          │
│                           ↓                                 │
│  1. NETTOYAGE                                               │
│  • Correction orthographe : "Bjr" → "Bonjour"               │
│  • Normalisation ponctuation : "!!!" → "!"                  │
│  • Gestion émojis : "😢" → "[tristesse]"                    │
│                           ↓                                 │
│  2. EXTRACTION D'ENTITÉS                                    │
│  • Lieu : "Yaoundé" → GPS: 3.8480°N, 11.5021°E            │
│  • Organisation : "hôpital X" → Type: Santé                 │
│                           ↓                                 │
│  3. DÉTECTION DE LANGUE                                     │
│  • Langue principale : Français (98% confiance)             │
│  • Mots empruntés : Aucun                                  │
│                           ↓                                 │
│  4. TOKENISATION INTELLIGENTE                               │
│  • Tokens : ["bonjour", "hôpital", "yaoundé",              │
│              "médicaments", "inadmissible", "malades",      │
│              "souffrent", "[tristesse]"]                    │
│                           ↓                                 │
│  Texte prétraité prêt pour l'IA                            │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### 2.3 Gestion des cas particuliers

| Cas              | Traitement                | Exemple                                    |
| ---------------- | ------------------------- | ------------------------------------------ |
| **Multilingue**  | Détection + séparation    | "Hello, comment allez-vous?"               |
| **Argot/Verlan** | Dictionnaire custom       | "C'est ouf" → "C'est fou"                  |
| **Abréviations** | Expansion                 | "CHU" → "Centre Hospitalier Universitaire" |
| **URLs**         | Extraction + validation   | Vérifier si lien actif                     |
| **Numéros**      | Anonymisation si sensible | Téléphone → [TELEPHONE]                    |

## 3. Étape 2 : Analyse par IA

### 3.1 Modèles utilisés

```
┌─────────────────────────────────────────────────────────────┐
│                    ENSEMBLE DE MODÈLES IA                   │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  🧠 Modèle Principal (BERT Multilingue)                     │
│  • Compréhension profonde du texte                         │
│  • Entraîné sur corpus africain + français                 │
│  • Fine-tuné sur expressions PillarScan                    │
│                                                             │
│  🎯 Modèles Spécialisés :                                   │
│  ├─ Classification Piliers : Précision 94%                 │
│  ├─ Analyse Sentiments : Précision 89%                     │
│  ├─ Détection Urgence : Précision 91%                      │
│  ├─ Extraction Entités : Précision 87%                     │
│  └─ Génération Titres : Pertinence 85%                     │
│                                                             │
│  🔄 Apprentissage Continu :                                 │
│  • Feedback utilisateurs intégré                           │
│  • Ré-entraînement mensuel                                 │
│  • A/B testing nouveaux modèles                            │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### 3.2 Processus d'analyse multi-passes

```
                 Expression prétraitée
                         │
    ┌────────────────────┴────────────────────┐
    │                                         │
    ▼                                         ▼
PASSE 1: Compréhension               PASSE 2: Classification
• Analyse sémantique                 • Matching piliers
• Extraction concepts                • Scoring confiance
• Contexte global                    • Multi-label possible
    │                                         │
    └────────────────────┬────────────────────┘
                         │
                         ▼
                 PASSE 3: Enrichissement
                 • Sentiment analysis
                 • Urgence detection
                 • Keywords extraction
                 • Title generation
                         │
                         ▼
                 Résultat structuré
```

### 3.3 Exemple de classification détaillée

```json
{
    "texte_original": "L'hôpital X à Yaoundé n'a plus de médicaments...",
    "analyse": {
        "piliers_detectes": [
            {
                "code": "SANTE",
                "score": 0.92,
                "indices": ["hôpital", "médicaments", "malades"],
                "sous_piliers": [
                    { "code": "ACCES_SOINS", "score": 0.88 },
                    { "code": "QUALITE_SOINS", "score": 0.75 }
                ]
            },
            {
                "code": "INFRASTRUCTURE",
                "score": 0.23,
                "indices": ["hôpital"],
                "sous_piliers": []
            }
        ],
        "sentiment": {
            "polarite": "negative",
            "score": -0.85,
            "emotions": {
                "colere": 0.72,
                "frustration": 0.81,
                "tristesse": 0.64,
                "desespoir": 0.45
            }
        },
        "urgence": {
            "niveau": 4,
            "justification": "Manque de médicaments = danger vital potentiel",
            "mots_declencheurs": ["plus de", "malades souffrent"]
        },
        "entites": [
            {
                "texte": "hôpital X",
                "type": "organisation",
                "sous_type": "etablissement_sante",
                "position": [2, 10]
            },
            {
                "texte": "Yaoundé",
                "type": "lieu",
                "gps": { "lat": 3.848, "lng": 11.5021 },
                "position": [13, 20]
            }
        ],
        "titre_suggere": "Rupture de médicaments à l'hôpital X de Yaoundé",
        "mots_cles": ["rupture stock", "médicaments", "hôpital", "Yaoundé", "crise sanitaire"],
        "resume_auto": "Un citoyen signale une rupture totale de médicaments à l'hôpital X de Yaoundé, causant des souffrances aux patients."
    }
}
```

## 4. Étape 3 : Post-traitement

### 4.1 Validation et cohérence

```
┌─────────────────────────────────────────────────────────────┐
│                 CONTRÔLES DE COHÉRENCE                      │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ✓ Vérification des scores :                               │
│    • Au moins 1 pilier > 0.5 confiance                     │
│    • Somme des scores normalisée à 1                       │
│                                                             │
│  ✓ Cohérence géographique :                                │
│    • Yaoundé est bien au Cameroun ✓                        │
│    • GPS correspond à la ville ✓                           │
│                                                             │
│  ✓ Cohérence temporelle :                                   │
│    • Date événement < Date création ✓                      │
│    • Pas d'anachronisme détecté ✓                          │
│                                                             │
│  ✓ Détection anomalies :                                   │
│    • Pas de spam détecté ✓                                 │
│    • Contenu pertinent ✓                                   │
│    • Langue cohérente ✓                                    │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### 4.2 Enrichissement contextuel

| Type d'enrichissement     | Source              | Exemple                                     |
| ------------------------- | ------------------- | ------------------------------------------- |
| **Données géographiques** | Base interne        | Population, climat, contexte local          |
| **Historique entité**     | Expressions passées | "Hôpital X : 12 signalements similaires"    |
| **Tendances**             | Analyse temps réel  | "Ruptures médicaments : +45% ce mois"       |
| **Ressources**            | Base connaissances  | Contacts utiles, procédures                 |
| **Comparaisons**          | Données nationales  | "Taux rupture : Région 35% vs National 18%" |

## 5. Cas d'usage spécifiques

### 5.1 Expressions complexes multi-thèmes

```
Expression : "L'école de mon fils n'a pas d'eau potable, les
toilettes sont cassées, et les professeurs sont souvent absents.
En plus, la route pour y aller est impraticable."

Classification IA :
┌─────────────────────────────────────────┐
│ 🎓 ÉDUCATION (45%)                      │
│    • Conditions apprentissage           │
│    • Absentéisme enseignants           │
├─────────────────────────────────────────┤
│ 💧 INFRASTRUCTURE (35%)                 │
│    • Eau et assainissement             │
│    • État des routes                   │
├─────────────────────────────────────────┤
│ 🏥 SANTÉ (20%)                         │
│    • Risques sanitaires (toilettes)    │
│    • Accès eau potable                 │
└─────────────────────────────────────────┘
```

### 5.2 Expressions émotionnelles

```
┌─────────────────────────────────────────────────────────────┐
│              GESTION DES ÉMOTIONS FORTES                    │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  Expression : "JE N'EN PEUX PLUS !!! MA MÈRE EST MORTE     │
│  PARCE QU'IL N'Y AVAIT PAS D'AMBULANCE !!!"               │
│                                                             │
│  Analyse IA :                                               │
│  • Détection détresse : NIVEAU MAXIMAL                     │
│  • Déclenchement protocole support                         │
│  • Classification prioritaire                               │
│  • Suggestion ressources d'aide                            │
│  • Flag pour modération sensible                           │
│                                                             │
│  Actions automatiques :                                      │
│  ✓ Notification équipe support                             │
│  ✓ Priorisation validation                                 │
│  ✓ Préparation réponse empathique                         │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### 5.3 Expressions en langues locales

```
Système multilingue adaptatif :

Français    ────┐
Anglais     ────┤
Fulfulde    ────┤
Ewondo      ────┼──► Modèle Unifié ──► Classification
Bassa       ────┤      Multilingue       Universelle
Douala      ────┤
Autres      ────┘

Particularités :
• Détection automatique de la langue
• Traduction si nécessaire
• Préservation des nuances culturelles
• Apprentissage des expressions locales
```

## 6. Métriques et performance

### 6.1 Tableau de bord temps réel

```
┌─────────────────────────────────────────────────────────────┐
│              PERFORMANCE CLASSIFICATION IA                  │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  📊 Métriques dernières 24h :                              │
│                                                             │
│  Expressions traitées : 12,847                             │
│  Temps moyen/expression : 1.3s                             │
│  Taux de réussite : 99.2%                                  │
│                                                             │
│  Précision par pilier :                                    │
│  ├─ Santé :              ████████████░ 94%                 │
│  ├─ Éducation :          ███████████░░ 91%                 │
│  ├─ Infrastructure :     ████████████░ 93%                 │
│  ├─ Économie :           ██████████░░░ 87%                 │
│  └─ Autres :             █████████░░░░ 85%                 │
│                                                             │
│  Distribution sentiments :                                  │
│  😊 Positif : 18%  😐 Neutre : 27%  😢 Négatif : 55%     │
│                                                             │
│  Alertes :                                                  │
│  ⚠️ Pic urgences détecté région Nord (+300%)               │
│  ⚠️ Nouveau vocabulaire émergent : "crise eau"             │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### 6.2 Métriques détaillées

| Métrique              | Valeur | Objectif | Statut       |
| --------------------- | ------ | -------- | ------------ |
| **Précision globale** | 91.5%  | 90%      | ✅ Atteint   |
| **Rappel (Recall)**   | 88.3%  | 85%      | ✅ Atteint   |
| **F1-Score**          | 89.8%  | 87%      | ✅ Atteint   |
| **Latence P95**       | 2.1s   | < 3s     | ✅ OK        |
| **Latence P99**       | 4.7s   | < 5s     | ✅ OK        |
| **Erreurs**           | 0.8%   | < 2%     | ✅ Excellent |

## 7. Apprentissage continu

### 7.1 Boucle de feedback

```
     Utilisateur valide/corrige
              │
              ▼
        Base feedback
              │
              ▼
    Analyse patterns erreurs
              │
              ▼
    Dataset enrichi (mensuel)
              │
              ▼
    Ré-entraînement modèle
              │
              ▼
    A/B test nouveau modèle
              │
              ▼
    Déploiement si meilleur
```

### 7.2 Sources d'apprentissage

1. **Corrections utilisateurs** : Quand ils modifient la classification
2. **Validations modérateurs** : Décisions des validateurs
3. **Nouveaux contextes** : Événements émergents (ex: pandémie)
4. **Vocabulaire évolutif** : Nouveaux termes, argot
5. **Patterns régionaux** : Spécificités locales

## 8. Gestion des erreurs et cas limites

### 8.1 Stratégies de fallback

```
┌─────────────────────────────────────────────────────────────┐
│                 CASCADE DE FALLBACK                         │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  Tentative 1 : Modèle principal                            │
│       ↓ (échec ou confiance < 0.3)                         │
│  Tentative 2 : Modèle généraliste                          │
│       ↓ (échec)                                             │
│  Tentative 3 : Règles heuristiques                         │
│       ↓ (échec)                                             │
│  Tentative 4 : Classification manuelle                      │
│       ↓                                                     │
│  File d'attente modération prioritaire                      │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### 8.2 Cas problématiques courants

| Cas                   | Problème            | Solution                   |
| --------------------- | ------------------- | -------------------------- |
| **Texte trop court**  | "Rien ne va"        | Demander plus de détails   |
| **Spam/Répétitions**  | "test test test"    | Filtrage + blocage         |
| **Hors sujet**        | Publicité, etc.     | Rejet automatique          |
| **Injures**           | Contenu haineux     | Modération + avertissement |
| **Données sensibles** | Numéros carte, etc. | Masquage automatique       |

## 9. Intégration avec Strapi

### 9.1 Architecture technique

```
┌─────────────────────────────────────────────────────────────┐
│                  FLUX D'INTÉGRATION                         │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  Strapi Backend                     Service IA              │
│  ┌─────────────┐      API REST     ┌──────────────┐       │
│  │ Expression  │ ─────────────────►│ Classification│       │
│  │ Created     │    POST /classify  │ Microservice │       │
│  └─────────────┘                    └──────┬───────┘       │
│         ▲                                   │               │
│         │          Webhook                  │               │
│         └───────────────────────────────────┘               │
│                  PUT /expressions/{id}                      │
│                                                             │
│  Données envoyées :           Données reçues :             │
│  • Texte expression           • Classification complète    │
│  • Métadonnées                • Scores confiance           │
│  • Contexte utilisateur       • Enrichissements            │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### 9.2 Optimisations

1. **Queue asynchrone** : RabbitMQ pour gérer les pics
2. **Cache résultats** : Redis pour expressions similaires
3. **Batch processing** : Grouper les requêtes
4. **Circuit breaker** : Protection surcharge
5. **Monitoring** : Prometheus + Grafana

## 10. Évolutions futures

### 10.1 Roadmap IA

```
2024 Q4 : Voice-to-text (expressions orales)
2025 Q1 : Analyse images (photos preuves)
2025 Q2 : Prédiction tendances
2025 Q3 : Chatbot assistance
2025 Q4 : IA explicable (XAI)
```

### 10.2 Innovations envisagées

- **Classification proactive** : Suggérer des piliers pendant la saisie
- **Détection anomalies** : Alertes sur patterns inhabituels
- **Analyse vidéo** : Pour témoignages vidéo
- **Traduction temps réel** : 50+ langues africaines
- **Personnalisation** : Modèles adaptés par région

Ce système de classification IA transforme des milliers d'expressions brutes en intelligence collective structurée, permettant une compréhension fine et temps réel des préoccupations citoyennes.
