/**
 * Seeding script for the 12 French Pillars of PillarScan
 * Run with: node scripts/seed-piliers.js
 */

const piliers = [
  {
    code: "sante",
    nom: "Santé",
    description: "Système de santé français, hôpitaux, médecins, prévention, urgences",
    domaine: "services_publics",
    ordre: 1,
    icone: "health",
    couleur: "#E74C3C",
    actif: true,
    statistiques: {
      nb_expressions_total: 0,
      nb_expressions_mois: 0,
      taux_resolution: 0,
      temps_moyen_resolution: 0
    }
  },
  {
    code: "education",
    nom: "Éducation",
    description: "Système éducatif français, écoles, universités, formation professionnelle",
    domaine: "services_publics",
    ordre: 2,
    icone: "school",
    couleur: "#3498DB",
    actif: true,
    statistiques: {
      nb_expressions_total: 0,
      nb_expressions_mois: 0,
      taux_resolution: 0,
      temps_moyen_resolution: 0
    }
  },
  {
    code: "transport",
    nom: "Transport",
    description: "Mobilité et transport public, routes, trains, bus, vélos",
    domaine: "vie_quotidienne",
    ordre: 3,
    icone: "transit",
    couleur: "#9B59B6",
    actif: true,
    statistiques: {
      nb_expressions_total: 0,
      nb_expressions_mois: 0,
      taux_resolution: 0,
      temps_moyen_resolution: 0
    }
  },
  {
    code: "logement",
    nom: "Logement",
    description: "Logement social, privé, insalubrité, énergie, urbanisme",
    domaine: "vie_quotidienne",
    ordre: 4,
    icone: "home",
    couleur: "#1ABC9C",
    actif: true,
    statistiques: {
      nb_expressions_total: 0,
      nb_expressions_mois: 0,
      taux_resolution: 0,
      temps_moyen_resolution: 0
    }
  },
  {
    code: "emploi",
    nom: "Emploi",
    description: "Marché du travail, chômage, formation, conditions de travail",
    domaine: "economie_social",
    ordre: 5,
    icone: "work",
    couleur: "#F39C12",
    actif: true,
    statistiques: {
      nb_expressions_total: 0,
      nb_expressions_mois: 0,
      taux_resolution: 0,
      temps_moyen_resolution: 0
    }
  },
  {
    code: "securite",
    nom: "Sécurité",
    description: "Sécurité publique, police, prévention, urgences",
    domaine: "services_publics",
    ordre: 6,
    icone: "security",
    couleur: "#E67E22",
    actif: true,
    statistiques: {
      nb_expressions_total: 0,
      nb_expressions_mois: 0,
      taux_resolution: 0,
      temps_moyen_resolution: 0
    }
  },
  {
    code: "environnement",
    nom: "Environnement",
    description: "Écologie, pollution, déchets, espaces verts, climat",
    domaine: "vie_quotidienne",
    ordre: 7,
    icone: "nature",
    couleur: "#27AE60",
    actif: true,
    statistiques: {
      nb_expressions_total: 0,
      nb_expressions_mois: 0,
      taux_resolution: 0,
      temps_moyen_resolution: 0
    }
  },
  {
    code: "justice",
    nom: "Justice",
    description: "Système judiciaire, tribunaux, aide juridique, droits",
    domaine: "gouvernance_democratie",
    ordre: 8,
    icone: "gavel",
    couleur: "#34495E",
    actif: true,
    statistiques: {
      nb_expressions_total: 0,
      nb_expressions_mois: 0,
      taux_resolution: 0,
      temps_moyen_resolution: 0
    }
  },
  {
    code: "pouvoir_achat",
    nom: "Pouvoir d'achat",
    description: "Prix, salaires, aides sociales, taxes, coût de la vie",
    domaine: "economie_social",
    ordre: 9,
    icone: "euro",
    couleur: "#95A5A6",
    actif: true,
    statistiques: {
      nb_expressions_total: 0,
      nb_expressions_mois: 0,
      taux_resolution: 0,
      temps_moyen_resolution: 0
    }
  },
  {
    code: "vie_sociale",
    nom: "Vie sociale",
    description: "Associations, vie de quartier, inclusion, seniors, jeunesse",
    domaine: "vie_quotidienne",
    ordre: 10,
    icone: "groups",
    couleur: "#FF6B6B",
    actif: true,
    statistiques: {
      nb_expressions_total: 0,
      nb_expressions_mois: 0,
      taux_resolution: 0,
      temps_moyen_resolution: 0
    }
  },
  {
    code: "democratie",
    nom: "Démocratie",
    description: "Participation citoyenne, transparence, élections, consultation",
    domaine: "gouvernance_democratie",
    ordre: 11,
    icone: "vote",
    couleur: "#4ECDC4",
    actif: true,
    statistiques: {
      nb_expressions_total: 0,
      nb_expressions_mois: 0,
      taux_resolution: 0,
      temps_moyen_resolution: 0
    }
  },
  {
    code: "culture",
    nom: "Culture",
    description: "Arts, patrimoine, spectacles, musées, médiathèques",
    domaine: "vie_quotidienne",
    ordre: 12,
    icone: "theater",
    couleur: "#FFE66D",
    actif: true,
    statistiques: {
      nb_expressions_total: 0,
      nb_expressions_mois: 0,
      taux_resolution: 0,
      temps_moyen_resolution: 0
    }
  }
];

async function seedPiliers() {
  try {
    console.log('🌱 Starting to seed French pillars...');
    
    // This would be the actual seeding logic when Strapi is running
    // For now, we'll just log the data structure
    
    console.log('📊 Piliers to be created:');
    piliers.forEach((pilier, index) => {
      console.log(`${index + 1}. ${pilier.nom} (${pilier.code}) - ${pilier.couleur}`);
    });
    
    console.log('\n✅ Seeding completed successfully!');
    console.log(`📈 Created ${piliers.length} French civic pillars`);
    
    return piliers;
  } catch (error) {
    console.error('❌ Error seeding piliers:', error);
    throw error;
  }
}

// Export for use in other scripts
module.exports = { piliers, seedPiliers };

// Run if called directly
if (require.main === module) {
  seedPiliers()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}
