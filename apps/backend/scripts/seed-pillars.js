#!/usr/bin/env node

/**
 * Seed script for PillarScan - Creates the 12 pillars of French society
 * Run with: node scripts/seed-pillars.js
 */

const path = require('path');

// Ensure we're in the backend directory
process.chdir(path.resolve(__dirname, '..'));

async function seedPillars() {
  console.log('🌱 Starting pillar seeding...');
  
  let strapi;
  
  try {
    // Initialize Strapi
    const { createStrapi } = require('@strapi/strapi');
    strapi = await createStrapi().load();
    
    console.log('✅ Strapi instance loaded');
    
    // Define the 12 pillars of French society
    const pillars = [
      {
        code: 'SANTE',
        nom: 'Santé',
        nom_en: 'Health',
        description: 'Système de santé publique, accès aux soins, prévention, hôpitaux, médecins généralistes et spécialistes.',
        domaine: 'services_publics',
        ordre: 1,
        icone: 'heart',
        couleur: '#e74c3c',
        actif: true,
        statistiques: {
          nb_expressions_total: 0,
          nb_expressions_mois: 0,
          taux_resolution: 0.0,
          temps_moyen_resolution: 0
        }
      },
      {
        code: 'EDUCATION',
        nom: 'Éducation',
        nom_en: 'Education',
        description: 'Système éducatif national, écoles, collèges, lycées, universités, formation professionnelle.',
        domaine: 'services_publics',
        ordre: 2,
        icone: 'academic-cap',
        couleur: '#3498db',
        actif: true,
        statistiques: {
          nb_expressions_total: 0,
          nb_expressions_mois: 0,
          taux_resolution: 0.0,
          temps_moyen_resolution: 0
        }
      },
      {
        code: 'SECURITE',
        nom: 'Sécurité',
        nom_en: 'Security',
        description: 'Sécurité publique, police nationale, gendarmerie, pompiers, sécurité civile.',
        domaine: 'services_publics',
        ordre: 3,
        icone: 'shield-check',
        couleur: '#2c3e50',
        actif: true,
        statistiques: {
          nb_expressions_total: 0,
          nb_expressions_mois: 0,
          taux_resolution: 0.0,
          temps_moyen_resolution: 0
        }
      },
      {
        code: 'TRANSPORT',
        nom: 'Transport',
        nom_en: 'Transportation',
        description: 'Transports en commun, infrastructure routière, SNCF, mobilité urbaine et rurale.',
        domaine: 'vie_quotidienne',
        ordre: 4,
        icone: 'truck',
        couleur: '#9b59b6',
        actif: true,
        statistiques: {
          nb_expressions_total: 0,
          nb_expressions_mois: 0,
          taux_resolution: 0.0,
          temps_moyen_resolution: 0
        }
      },
      {
        code: 'LOGEMENT',
        nom: 'Logement',
        nom_en: 'Housing',
        description: 'Politique du logement, logement social, urbanisme, rénovation urbaine.',
        domaine: 'vie_quotidienne',
        ordre: 5,
        icone: 'home',
        couleur: '#e67e22',
        actif: true,
        statistiques: {
          nb_expressions_total: 0,
          nb_expressions_mois: 0,
          taux_resolution: 0.0,
          temps_moyen_resolution: 0
        }
      },
      {
        code: 'ENVIRONNEMENT',
        nom: 'Environnement',
        nom_en: 'Environment',
        description: 'Écologie, développement durable, gestion des déchets, qualité de l\'air, espaces verts.',
        domaine: 'vie_quotidienne',
        ordre: 6,
        icone: 'leaf',
        couleur: '#27ae60',
        actif: true,
        statistiques: {
          nb_expressions_total: 0,
          nb_expressions_mois: 0,
          taux_resolution: 0.0,
          temps_moyen_resolution: 0
        }
      },
      {
        code: 'ECONOMIE',
        nom: 'Économie',
        nom_en: 'Economy',
        description: 'Politique économique, emploi, entreprises, fiscalité, développement économique local.',
        domaine: 'economie_social',
        ordre: 7,
        icone: 'chart-bar',
        couleur: '#f39c12',
        actif: true,
        statistiques: {
          nb_expressions_total: 0,
          nb_expressions_mois: 0,
          taux_resolution: 0.0,
          temps_moyen_resolution: 0
        }
      },
      {
        code: 'SOCIAL',
        nom: 'Action Sociale',
        nom_en: 'Social Action',
        description: 'Aide sociale, solidarité, lutte contre la pauvreté, services sociaux, inclusion.',
        domaine: 'economie_social',
        ordre: 8,
        icone: 'users',
        couleur: '#e91e63',
        actif: true,
        statistiques: {
          nb_expressions_total: 0,
          nb_expressions_mois: 0,
          taux_resolution: 0.0,
          temps_moyen_resolution: 0
        }
      },
      {
        code: 'CULTURE',
        nom: 'Culture',
        nom_en: 'Culture',
        description: 'Patrimoine culturel, arts, spectacles, bibliothèques, musées, événements culturels.',
        domaine: 'economie_social',
        ordre: 9,
        icone: 'building-library',
        couleur: '#8e44ad',
        actif: true,
        statistiques: {
          nb_expressions_total: 0,
          nb_expressions_mois: 0,
          taux_resolution: 0.0,
          temps_moyen_resolution: 0
        }
      },
      {
        code: 'JUSTICE',
        nom: 'Justice',
        nom_en: 'Justice',
        description: 'Système judiciaire, tribunaux, accès au droit, médiation, droits des citoyens.',
        domaine: 'gouvernance_democratie',
        ordre: 10,
        icone: 'scale',
        couleur: '#34495e',
        actif: true,
        statistiques: {
          nb_expressions_total: 0,
          nb_expressions_mois: 0,
          taux_resolution: 0.0,
          temps_moyen_resolution: 0
        }
      },
      {
        code: 'ADMINISTRATION',
        nom: 'Administration',
        nom_en: 'Administration',
        description: 'Services publics administratifs, démarches citoyennes, état civil, fiscalité locale.',
        domaine: 'gouvernance_democratie',
        ordre: 11,
        icone: 'building-office',
        couleur: '#16a085',
        actif: true,
        statistiques: {
          nb_expressions_total: 0,
          nb_expressions_mois: 0,
          taux_resolution: 0.0,
          temps_moyen_resolution: 0
        }
      },
      {
        code: 'PARTICIPATION',
        nom: 'Participation Citoyenne',
        nom_en: 'Citizen Participation',
        description: 'Démocratie participative, consultations publiques, conseils citoyens, vie démocratique locale.',
        domaine: 'gouvernance_democratie',
        ordre: 12,
        icone: 'megaphone',
        couleur: '#c0392b',
        actif: true,
        statistiques: {
          nb_expressions_total: 0,
          nb_expressions_mois: 0,
          taux_resolution: 0.0,
          temps_moyen_resolution: 0
        }
      }
    ];

    console.log(`📝 Creating ${pillars.length} pillars...`);

    // Get the pilier service
    const pilierService = strapi.service('api::pilier.pilier');
    
    // Check if pillars already exist
    const existingPillars = await strapi.db.query('api::pilier.pilier').findMany();
    
    if (existingPillars.length > 0) {
      console.log(`⚠️ Found ${existingPillars.length} existing pillars. Updating them...`);
      
      // Update existing pillars
      for (const pillar of pillars) {
        const existing = existingPillars.find(p => p.code === pillar.code);
        if (existing) {
          await strapi.db.query('api::pilier.pilier').update({
            where: { id: existing.id },
            data: pillar
          });
          console.log(`✅ Updated pillar: ${pillar.nom}`);
        } else {
          await strapi.db.query('api::pilier.pilier').create({
            data: pillar
          });
          console.log(`✅ Created pillar: ${pillar.nom}`);
        }
      }
    } else {
      // Create new pillars
      for (const pillar of pillars) {
        await strapi.db.query('api::pilier.pilier').create({
          data: pillar
        });
        console.log(`✅ Created pillar: ${pillar.nom}`);
      }
    }

    console.log('🎉 Pillar seeding completed successfully!');
    console.log(`📊 Total pillars in database: ${await strapi.db.query('api::pilier.pilier').count()}`);
    
  } catch (error) {
    console.error('❌ Error during seeding:', error);
    throw error;
  } finally {
    if (strapi) {
      await strapi.destroy();
    }
    process.exit(0);
  }
}

// Run the seeding
if (require.main === module) {
  seedPillars().catch((error) => {
    console.error('❌ Seeding failed:', error);
    process.exit(1);
  });
}

module.exports = seedPillars;