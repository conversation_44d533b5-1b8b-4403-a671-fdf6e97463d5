/**
 * PillarScan Database Seeding Script
 * Seeds the database with the 12 pillars of French society and their sub-pillars
 */

const PILLIERS_DATA = [
  {
    code: "SANTE",
    nom: "Santé",
    nom_en: "Health",
    description: "Système de santé, accès aux soins, qualité des services médicaux",
    domaine: "services_publics",
    ordre: 1,
    icone: "health",
    couleur: "#E74C3C",
    sous_piliers: [
      { code: "SANTE_ACCESS", nom: "Accès aux soins", ordre: 1 },
      { code: "SANTE_QUALITE", nom: "Qualité des soins", ordre: 2 },
      { code: "SANTE_URGENCES", nom: "Services d'urgence", ordre: 3 },
      { code: "SANTE_PREVENTION", nom: "Prévention", ordre: 4 },
      { code: "SANTE_MEDICAMENTS", nom: "Médicaments", ordre: 5 },
      { code: "SANTE_HANDICAP", nom: "Handicap et dépendance", ordre: 6 }
    ]
  },
  {
    code: "EDUCATION",
    nom: "Éducation",
    nom_en: "Education",
    description: "Système éducatif, formation, enseignement supérieur",
    domaine: "services_publics",
    ordre: 2,
    icone: "school",
    couleur: "#3498DB",
    sous_piliers: [
      { code: "EDU_PRIMAIRE", nom: "École primaire", ordre: 1 },
      { code: "EDU_SECONDAIRE", nom: "Collège et lycée", ordre: 2 },
      { code: "EDU_SUPERIEUR", nom: "Enseignement supérieur", ordre: 3 },
      { code: "EDU_FORMATION", nom: "Formation professionnelle", ordre: 4 },
      { code: "EDU_PERISCOLAIRE", nom: "Activités périscolaires", ordre: 5 },
      { code: "EDU_NUMERIQUE", nom: "Éducation numérique", ordre: 6 }
    ]
  },
  {
    code: "TRANSPORT",
    nom: "Transport",
    nom_en: "Transportation",
    description: "Transports publics, infrastructure routière, mobilité",
    domaine: "vie_quotidienne",
    ordre: 3,
    icone: "transit",
    couleur: "#9B59B6",
    sous_piliers: [
      { code: "TRANSPORT_PUBLIC", nom: "Transports en commun", ordre: 1 },
      { code: "TRANSPORT_ROUTES", nom: "Routes et autoroutes", ordre: 2 },
      { code: "TRANSPORT_TRAIN", nom: "Transport ferroviaire", ordre: 3 },
      { code: "TRANSPORT_VELO", nom: "Mobilité douce", ordre: 4 },
      { code: "TRANSPORT_PARKING", nom: "Stationnement", ordre: 5 },
      { code: "TRANSPORT_ACCESSIBILITE", nom: "Accessibilité PMR", ordre: 6 }
    ]
  },
  {
    code: "LOGEMENT",
    nom: "Logement",
    nom_en: "Housing",
    description: "Logement social, privé, conditions d'habitat",
    domaine: "vie_quotidienne",
    ordre: 4,
    icone: "home",
    couleur: "#1ABC9C",
    sous_piliers: [
      { code: "LOGEMENT_SOCIAL", nom: "Logement social", ordre: 1 },
      { code: "LOGEMENT_PRIVE", nom: "Marché privé", ordre: 2 },
      { code: "LOGEMENT_INSALUBRITE", nom: "Insalubrité", ordre: 3 },
      { code: "LOGEMENT_ENERGIE", nom: "Efficacité énergétique", ordre: 4 },
      { code: "LOGEMENT_COPROPRIETE", nom: "Copropriétés", ordre: 5 },
      { code: "LOGEMENT_JEUNES", nom: "Logement des jeunes", ordre: 6 }
    ]
  },
  {
    code: "EMPLOI",
    nom: "Emploi",
    nom_en: "Employment",
    description: "Marché du travail, chômage, conditions de travail",
    domaine: "economie_social",
    ordre: 5,
    icone: "work",
    couleur: "#F39C12",
    sous_piliers: [
      { code: "EMPLOI_CHOMAGE", nom: "Chômage", ordre: 1 },
      { code: "EMPLOI_FORMATION", nom: "Formation professionnelle", ordre: 2 },
      { code: "EMPLOI_CONDITIONS", nom: "Conditions de travail", ordre: 3 },
      { code: "EMPLOI_DISCRIMINATION", nom: "Discrimination", ordre: 4 },
      { code: "EMPLOI_JEUNES", nom: "Emploi des jeunes", ordre: 5 },
      { code: "EMPLOI_SENIORS", nom: "Emploi des seniors", ordre: 6 }
    ]
  },
  {
    code: "SECURITE",
    nom: "Sécurité",
    nom_en: "Security",
    description: "Sécurité publique, police, prévention de la délinquance",
    domaine: "services_publics",
    ordre: 6,
    icone: "security",
    couleur: "#E67E22",
    sous_piliers: [
      { code: "SECURITE_POLICE", nom: "Police et gendarmerie", ordre: 1 },
      { code: "SECURITE_PREVENTION", nom: "Prévention", ordre: 2 },
      { code: "SECURITE_URGENCES", nom: "Services d'urgence", ordre: 3 },
      { code: "SECURITE_VOISINAGE", nom: "Tranquillité publique", ordre: 4 },
      { code: "SECURITE_ROUTIERE", nom: "Sécurité routière", ordre: 5 },
      { code: "SECURITE_CYBER", nom: "Cybersécurité", ordre: 6 }
    ]
  },
  {
    code: "ENVIRONNEMENT",
    nom: "Environnement",
    nom_en: "Environment",
    description: "Écologie, pollution, espaces verts, développement durable",
    domaine: "vie_quotidienne",
    ordre: 7,
    icone: "nature",
    couleur: "#27AE60",
    sous_piliers: [
      { code: "ENV_POLLUTION", nom: "Pollution", ordre: 1 },
      { code: "ENV_DECHETS", nom: "Gestion des déchets", ordre: 2 },
      { code: "ENV_ESPACES_VERTS", nom: "Espaces verts", ordre: 3 },
      { code: "ENV_CLIMAT", nom: "Changement climatique", ordre: 4 },
      { code: "ENV_EAU", nom: "Qualité de l'eau", ordre: 5 },
      { code: "ENV_BIODIVERSITE", nom: "Biodiversité", ordre: 6 }
    ]
  },
  {
    code: "JUSTICE",
    nom: "Justice",
    nom_en: "Justice",
    description: "Système judiciaire, accès au droit, aide juridique",
    domaine: "services_publics",
    ordre: 8,
    icone: "gavel",
    couleur: "#34495E",
    sous_piliers: [
      { code: "JUSTICE_TRIBUNAUX", nom: "Tribunaux", ordre: 1 },
      { code: "JUSTICE_AIDE", nom: "Aide juridique", ordre: 2 },
      { code: "JUSTICE_MEDIATION", nom: "Médiation", ordre: 3 },
      { code: "JUSTICE_DROITS", nom: "Droits des citoyens", ordre: 4 },
      { code: "JUSTICE_PRISON", nom: "Système pénitentiaire", ordre: 5 },
      { code: "JUSTICE_VICTIMES", nom: "Aide aux victimes", ordre: 6 }
    ]
  },
  {
    code: "POUVOIR_ACHAT",
    nom: "Pouvoir d'achat",
    nom_en: "Purchasing Power",
    description: "Coût de la vie, prix, salaires, aides sociales",
    domaine: "economie_social",
    ordre: 9,
    icone: "euro",
    couleur: "#95A5A6",
    sous_piliers: [
      { code: "PA_PRIX", nom: "Prix et inflation", ordre: 1 },
      { code: "PA_SALAIRES", nom: "Salaires", ordre: 2 },
      { code: "PA_AIDES", nom: "Aides sociales", ordre: 3 },
      { code: "PA_TAXES", nom: "Taxes et impôts", ordre: 4 },
      { code: "PA_ENERGIE", nom: "Coût de l'énergie", ordre: 5 },
      { code: "PA_ALIMENTATION", nom: "Coût de l'alimentation", ordre: 6 }
    ]
  },
  {
    code: "VIE_SOCIALE",
    nom: "Vie sociale",
    nom_en: "Social Life",
    description: "Lien social, associations, inclusion, solidarité",
    domaine: "vie_quotidienne",
    ordre: 10,
    icone: "groups",
    couleur: "#FF6B6B",
    sous_piliers: [
      { code: "VS_ASSOCIATIONS", nom: "Vie associative", ordre: 1 },
      { code: "VS_QUARTIER", nom: "Vie de quartier", ordre: 2 },
      { code: "VS_INCLUSION", nom: "Inclusion sociale", ordre: 3 },
      { code: "VS_SENIORS", nom: "Seniors", ordre: 4 },
      { code: "VS_JEUNES", nom: "Jeunesse", ordre: 5 },
      { code: "VS_FAMILLE", nom: "Famille", ordre: 6 }
    ]
  },
  {
    code: "DEMOCRATIE",
    nom: "Démocratie",
    nom_en: "Democracy",
    description: "Participation citoyenne, transparence, élections",
    domaine: "gouvernance_democratie",
    ordre: 11,
    icone: "vote",
    couleur: "#4ECDC4",
    sous_piliers: [
      { code: "DEM_PARTICIPATION", nom: "Participation citoyenne", ordre: 1 },
      { code: "DEM_TRANSPARENCE", nom: "Transparence", ordre: 2 },
      { code: "DEM_ELECTIONS", nom: "Élections", ordre: 3 },
      { code: "DEM_CONSULTATION", nom: "Consultation publique", ordre: 4 },
      { code: "DEM_INFORMATION", nom: "Information publique", ordre: 5 },
      { code: "DEM_NUMERIQUE", nom: "Démocratie numérique", ordre: 6 }
    ]
  },
  {
    code: "CULTURE",
    nom: "Culture",
    nom_en: "Culture",
    description: "Patrimoine, spectacles, arts, médiathèques",
    domaine: "vie_quotidienne",
    ordre: 12,
    icone: "theater",
    couleur: "#FFE66D",
    sous_piliers: [
      { code: "CULT_MUSEES", nom: "Musées", ordre: 1 },
      { code: "CULT_SPECTACLES", nom: "Spectacles", ordre: 2 },
      { code: "CULT_PATRIMOINE", nom: "Patrimoine", ordre: 3 },
      { code: "CULT_MEDIATHEQUES", nom: "Médiathèques", ordre: 4 },
      { code: "CULT_FESTIVALS", nom: "Festivals", ordre: 5 },
      { code: "CULT_CREATION", nom: "Création artistique", ordre: 6 }
    ]
  }
];

async function seedPillars() {
  console.log('🌱 Starting PillarScan database seeding...');
  
  try {
    // Clear existing data
    console.log('🗑️ Clearing existing pillars and sub-pillars...');
    await strapi.db.query('api::sous-pilier.sous-pilier').deleteMany({});
    await strapi.db.query('api::pilier.pilier').deleteMany({});
    
    // Seed pillars and sub-pillars
    for (const pilierData of PILLIERS_DATA) {
      console.log(`📊 Creating pillar: ${pilierData.nom}`);
      
      const { sous_piliers, ...pilierAttributes } = pilierData;
      
      // Create the pillar
      const pilier = await strapi.db.query('api::pilier.pilier').create({
        data: {
          ...pilierAttributes,
          actif: true,
          statistiques: {
            nb_expressions_total: 0,
            nb_expressions_mois: 0,
            taux_resolution: 0.0,
            temps_moyen_resolution: 0
          }
        }
      });
      
      // Create sub-pillars
      for (const sousPilierData of sous_piliers) {
        console.log(`  📋 Creating sub-pillar: ${sousPilierData.nom}`);
        
        await strapi.db.query('api::sous-pilier.sous-pilier').create({
          data: {
            ...sousPilierData,
            pilier: pilier.id,
            actif: true,
            statistiques: {
              nb_expressions_total: 0,
              nb_expressions_mois: 0,
              taux_resolution: 0.0
            },
            mots_cles: []
          }
        });
      }
    }
    
    console.log('✅ Database seeding completed successfully!');
    console.log(`📊 Created ${PILLIERS_DATA.length} pillars`);
    console.log(`📋 Created ${PILLIERS_DATA.reduce((acc, p) => acc + p.sous_piliers.length, 0)} sub-pillars`);
    
  } catch (error) {
    console.error('❌ Error during seeding:', error);
    throw error;
  }
}

// Export for use in Strapi console
module.exports = { seedPillars };
