/**
 * SQLite seed script for PillarScan - Creates the 12 pillars of French society
 * Run with: node scripts/sqlite-seed.js
 */

const path = require('path');
const Database = require('better-sqlite3');

async function seedPillars() {
  console.log('🌱 Starting SQLite pillar seeding...');
  
  // Database path - same as configured in database.ts
  const dbPath = path.join(__dirname, '..', '.tmp', 'data.db');
  console.log(`📁 Database path: ${dbPath}`);
  
  let db;
  
  try {
    // Open SQLite database
    db = new Database(dbPath);
    console.log('✅ Connected to SQLite database');

    // Check if pilliers table exists
    const tableCheck = db.prepare(`
      SELECT name FROM sqlite_master 
      WHERE type='table' AND name='piliers'
    `).get();

    if (!tableCheck) {
      console.log('⚠️ Piliers table does not exist yet. Please run the Strapi server first to create tables.');
      console.log('   Run: pnpm develop');
      return;
    }

    console.log('✅ Piliers table found');

    // Define the 12 pillars
    const pillars = [
      {
        code: 'SANTE',
        nom: 'Santé',
        nom_en: 'Health',
        description: 'Système de santé publique, accès aux soins, prévention, hôpitaux, médecins généralistes et spécialistes.',
        domaine: 'services_publics',
        ordre: 1,
        icone: 'heart',
        couleur: '#e74c3c',
        actif: true,
        statistiques: JSON.stringify({
          nb_expressions_total: 0,
          nb_expressions_mois: 0,
          taux_resolution: 0.0,
          temps_moyen_resolution: 0
        }),
        metadata: JSON.stringify({})
      },
      {
        code: 'EDUCATION',
        nom: 'Éducation',
        nom_en: 'Education',
        description: 'Système éducatif national, écoles, collèges, lycées, universités, formation professionnelle.',
        domaine: 'services_publics',
        ordre: 2,
        icone: 'academic-cap',
        couleur: '#3498db',
        actif: true,
        statistiques: JSON.stringify({
          nb_expressions_total: 0,
          nb_expressions_mois: 0,
          taux_resolution: 0.0,
          temps_moyen_resolution: 0
        }),
        metadata: JSON.stringify({})
      },
      {
        code: 'SECURITE',
        nom: 'Sécurité',
        nom_en: 'Security',
        description: 'Sécurité publique, police nationale, gendarmerie, pompiers, sécurité civile.',
        domaine: 'services_publics',
        ordre: 3,
        icone: 'shield-check',
        couleur: '#2c3e50',
        actif: true,
        statistiques: JSON.stringify({
          nb_expressions_total: 0,
          nb_expressions_mois: 0,
          taux_resolution: 0.0,
          temps_moyen_resolution: 0
        }),
        metadata: JSON.stringify({})
      },
      {
        code: 'TRANSPORT',
        nom: 'Transport',
        nom_en: 'Transportation',
        description: 'Transports en commun, infrastructure routière, SNCF, mobilité urbaine et rurale.',
        domaine: 'vie_quotidienne',
        ordre: 4,
        icone: 'truck',
        couleur: '#9b59b6',
        actif: true,
        statistiques: JSON.stringify({
          nb_expressions_total: 0,
          nb_expressions_mois: 0,
          taux_resolution: 0.0,
          temps_moyen_resolution: 0
        }),
        metadata: JSON.stringify({})
      },
      {
        code: 'LOGEMENT',
        nom: 'Logement',
        nom_en: 'Housing',
        description: 'Politique du logement, logement social, urbanisme, rénovation urbaine.',
        domaine: 'vie_quotidienne',
        ordre: 5,
        icone: 'home',
        couleur: '#e67e22',
        actif: true,
        statistiques: JSON.stringify({
          nb_expressions_total: 0,
          nb_expressions_mois: 0,
          taux_resolution: 0.0,
          temps_moyen_resolution: 0
        }),
        metadata: JSON.stringify({})
      },
      {
        code: 'ENVIRONNEMENT',
        nom: 'Environnement',
        nom_en: 'Environment',
        description: 'Écologie, développement durable, gestion des déchets, qualité de l\'air, espaces verts.',
        domaine: 'vie_quotidienne',
        ordre: 6,
        icone: 'leaf',
        couleur: '#27ae60',
        actif: true,
        statistiques: JSON.stringify({
          nb_expressions_total: 0,
          nb_expressions_mois: 0,
          taux_resolution: 0.0,
          temps_moyen_resolution: 0
        }),
        metadata: JSON.stringify({})
      },
      {
        code: 'ECONOMIE',
        nom: 'Économie',
        nom_en: 'Economy',
        description: 'Politique économique, emploi, entreprises, fiscalité, développement économique local.',
        domaine: 'economie_social',
        ordre: 7,
        icone: 'chart-bar',
        couleur: '#f39c12',
        actif: true,
        statistiques: JSON.stringify({
          nb_expressions_total: 0,
          nb_expressions_mois: 0,
          taux_resolution: 0.0,
          temps_moyen_resolution: 0
        }),
        metadata: JSON.stringify({})
      },
      {
        code: 'SOCIAL',
        nom: 'Action Sociale',
        nom_en: 'Social Action',
        description: 'Aide sociale, solidarité, lutte contre la pauvreté, services sociaux, inclusion.',
        domaine: 'economie_social',
        ordre: 8,
        icone: 'users',
        couleur: '#e91e63',
        actif: true,
        statistiques: JSON.stringify({
          nb_expressions_total: 0,
          nb_expressions_mois: 0,
          taux_resolution: 0.0,
          temps_moyen_resolution: 0
        }),
        metadata: JSON.stringify({})
      },
      {
        code: 'CULTURE',
        nom: 'Culture',
        nom_en: 'Culture',
        description: 'Patrimoine culturel, arts, spectacles, bibliothèques, musées, événements culturels.',
        domaine: 'economie_social',
        ordre: 9,
        icone: 'building-library',
        couleur: '#8e44ad',
        actif: true,
        statistiques: JSON.stringify({
          nb_expressions_total: 0,
          nb_expressions_mois: 0,
          taux_resolution: 0.0,
          temps_moyen_resolution: 0
        }),
        metadata: JSON.stringify({})
      },
      {
        code: 'JUSTICE',
        nom: 'Justice',
        nom_en: 'Justice',
        description: 'Système judiciaire, tribunaux, accès au droit, médiation, droits des citoyens.',
        domaine: 'gouvernance_democratie',
        ordre: 10,
        icone: 'scale',
        couleur: '#34495e',
        actif: true,
        statistiques: JSON.stringify({
          nb_expressions_total: 0,
          nb_expressions_mois: 0,
          taux_resolution: 0.0,
          temps_moyen_resolution: 0
        }),
        metadata: JSON.stringify({})
      },
      {
        code: 'ADMINISTRATION',
        nom: 'Administration',
        nom_en: 'Administration',
        description: 'Services publics administratifs, démarches citoyennes, état civil, fiscalité locale.',
        domaine: 'gouvernance_democratie',
        ordre: 11,
        icone: 'building-office',
        couleur: '#16a085',
        actif: true,
        statistiques: JSON.stringify({
          nb_expressions_total: 0,
          nb_expressions_mois: 0,
          taux_resolution: 0.0,
          temps_moyen_resolution: 0
        }),
        metadata: JSON.stringify({})
      },
      {
        code: 'PARTICIPATION',
        nom: 'Participation Citoyenne',
        nom_en: 'Citizen Participation',
        description: 'Démocratie participative, consultations publiques, conseils citoyens, vie démocratique locale.',
        domaine: 'gouvernance_democratie',
        ordre: 12,
        icone: 'megaphone',
        couleur: '#c0392b',
        actif: true,
        statistiques: JSON.stringify({
          nb_expressions_total: 0,
          nb_expressions_mois: 0,
          taux_resolution: 0.0,
          temps_moyen_resolution: 0
        }),
        metadata: JSON.stringify({})
      }
    ];

    console.log(`📝 Seeding ${pillars.length} pillars...`);

    // Start transaction
    const transaction = db.transaction((pillars) => {
      // Clear existing pillars first
      const deleteStmt = db.prepare('DELETE FROM piliers');
      const deletedCount = deleteStmt.run().changes;
      console.log(`🗑️ Cleared ${deletedCount} existing pillars`);

      // Insert new pillars
      const insertStmt = db.prepare(`
        INSERT INTO piliers (
          code, nom, nom_en, description, domaine, ordre, icone, couleur, 
          actif, statistiques, metadata, created_at, updated_at
        ) VALUES (
          ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now')
        )
      `);

      for (const pillar of pillars) {
        insertStmt.run(
          pillar.code,
          pillar.nom,
          pillar.nom_en,
          pillar.description,
          pillar.domaine,
          pillar.ordre,
          pillar.icone,
          pillar.couleur,
          pillar.actif ? 1 : 0, // SQLite boolean
          pillar.statistiques,
          pillar.metadata
        );
        console.log(`✅ Created pillar: ${pillar.nom}`);
      }
    });

    // Execute transaction
    transaction(pillars);

    // Verify results
    const count = db.prepare('SELECT COUNT(*) as count FROM piliers').get();
    console.log(`🎉 Seeding completed! Total pillars: ${count.count}`);

    // Show all pillars
    const allPillars = db.prepare('SELECT code, nom, ordre FROM piliers ORDER BY ordre').all();
    console.log('\n📋 Pillars created:');
    allPillars.forEach((pillar, index) => {
      console.log(`   ${index + 1}. ${pillar.nom} (${pillar.code})`);
    });

  } catch (error) {
    console.error('❌ Error during seeding:', error.message);
    throw error;
  } finally {
    if (db) {
      db.close();
    }
  }
}

// Run the seeding
if (require.main === module) {
  seedPillars().catch((error) => {
    console.error('❌ Seeding failed:', error);
    process.exit(1);
  });
}

module.exports = seedPillars;