{"kind": "collectionType", "collectionName": "lieux", "info": {"singularName": "lieu", "pluralName": "lieux", "displayName": "<PERSON><PERSON>", "description": "Geographic locations for expressions and user residence"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"nom": {"type": "string", "required": true, "maxLength": 200, "description": "Nom du lieu"}, "type": {"type": "enumeration", "enum": ["adresse", "zone", "point_interet"], "default": "adresse", "required": true, "description": "Type de lieu (adresse, zone, point d'intérêt)"}, "niveau": {"type": "enumeration", "enum": ["pays", "region", "departement", "ville", "quartier", "rue"], "required": true, "description": "Niveau administratif du lieu"}, "coordonnees": {"type": "json", "required": true, "description": "Coordonnées géographiques (latitude, longitude)"}, "adresse_complete": {"type": "text", "description": "<PERSON>resse complète formatée"}, "code_postal": {"type": "string", "maxLength": 10, "description": "Code postal"}, "ville": {"type": "string", "maxLength": 100}, "departement": {"type": "string", "maxLength": 100}, "region": {"type": "string", "maxLength": 100}, "pays": {"type": "string", "maxLength": 2, "default": "FR"}, "parent": {"type": "relation", "relation": "manyToOne", "target": "api::lieu.lieu", "inversedBy": "enfants"}, "enfants": {"type": "relation", "relation": "oneToMany", "target": "api::lieu.lieu", "mappedBy": "parent"}, "expressions": {"type": "relation", "relation": "oneToMany", "target": "api::expression.expression", "mappedBy": "lieu"}, "residents": {"type": "relation", "relation": "oneToMany", "target": "api::profile.profile", "mappedBy": "lieu_residence"}, "entites": {"type": "relation", "relation": "oneToMany", "target": "api::entite.entite", "mappedBy": "lieu_principal"}, "actif": {"type": "boolean", "default": true, "required": true}, "verifie": {"type": "boolean", "default": false}, "metadata": {"type": "json", "default": {}}}}