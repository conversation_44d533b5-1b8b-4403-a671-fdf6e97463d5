{"kind": "collectionType", "collectionName": "piliers", "info": {"singularName": "pilier", "pluralName": "piliers", "displayName": "Pilier", "description": "The 12 pillars of French society for expression classification"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"code": {"type": "string", "required": true, "unique": true, "maxLength": 20, "description": "Code unique du pilier (ex: SANTE, EDUCATION)"}, "nom": {"type": "string", "required": true, "maxLength": 100, "description": "Nom du pilier en français"}, "nom_en": {"type": "string", "maxLength": 100, "description": "Nom du pilier en anglais"}, "description": {"type": "text", "required": true, "description": "Description détaillée du pilier"}, "domaine": {"type": "enumeration", "enum": ["services_publics", "vie_quotidienne", "economie_social", "gouvernance_democratie"], "required": true, "description": "Domaine principal du pilier"}, "ordre": {"type": "integer", "required": true, "min": 1, "max": 12, "description": "Ordre d'affichage du pilier (1-12)"}, "icone": {"type": "string", "required": true, "maxLength": 50, "description": "Nom de l'icône (ex: heart, shield)"}, "couleur": {"type": "string", "required": true, "maxLength": 7, "regex": "^#[0-9A-Fa-f]{6}$", "description": "Couleur du pilier au format hexadécimal (#RRGGBB)"}, "actif": {"type": "boolean", "default": true, "required": true, "description": "Indique si le pilier est actif et visible"}, "sous_piliers": {"type": "relation", "relation": "oneToMany", "target": "api::sous-pilier.sous-pilier", "mappedBy": "pilier", "description": "Sous-catégories de ce pilier"}, "expressions": {"type": "relation", "relation": "manyToMany", "target": "api::expression.expression", "mappedBy": "piliers", "description": "Expressions associées à ce pilier"}, "validateurs_specialistes": {"type": "relation", "relation": "manyToMany", "target": "api::validateur.validateur", "mappedBy": "specialites", "description": "Validateurs spécialistes de ce pilier"}, "statistiques": {"type": "json", "default": {"nb_expressions_total": 0, "nb_expressions_mois": 0, "taux_resolution": 0.0, "temps_moyen_resolution": 0}, "description": "Statistiques de performance du pilier"}, "metadata": {"type": "json", "default": {}, "description": "Métadonnées supplémentaires"}}}