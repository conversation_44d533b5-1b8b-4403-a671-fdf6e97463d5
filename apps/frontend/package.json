{"name": "frontend", "version": "0.1.0", "private": true, "description": "PillarScan Frontend - Citizen Expression Platform", "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@headlessui/react": "^2.0.4", "@heroicons/react": "^2.0.18", "axios": "^1.6.2", "clsx": "^2.0.0", "date-fns": "^3.0.6", "framer-motion": "^10.16.16", "mapbox-gl": "^3.0.1", "next": "15.3.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-map-gl": "^7.1.7", "react-query": "^3.39.3", "zustand": "^4.4.7"}, "devDependencies": {"@tailwindcss/forms": "^0.5.7", "@tailwindcss/postcss": "^4", "@tailwindcss/typography": "^0.5.10", "@types/mapbox-gl": "^3.1.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^8", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "typescript": "^5"}}