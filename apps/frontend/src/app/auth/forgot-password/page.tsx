"use client";

import React, { useState } from "react";
import <PERSON> from "next/link";
import { useRouter } from "next/navigation";
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import Input from "@/components/ui/Input";
import {
    EnvelopeIcon,
    ArrowLeftIcon,
    CheckCircleIcon,
    ExclamationTriangleIcon,
} from "@heroicons/react/24/outline";

export default function ForgotPasswordPage() {
    const router = useRouter();
    const [email, setEmail] = useState("");
    const [isLoading, setIsLoading] = useState(false);
    const [isSuccess, setIsSuccess] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setIsLoading(true);
        setError(null);

        try {
            // Validation
            if (!email.trim()) {
                throw new Error("L'adresse email est requise");
            }

            if (!/\S+@\S+\.\S+/.test(email)) {
                throw new Error("Veuillez entrer une adresse email valide");
            }

            // Simulate API call for password reset
            await new Promise((resolve) => setTimeout(resolve, 2000));

            // For demo purposes, we'll simulate success
            setIsSuccess(true);
        } catch (error: any) {
            setError(error.message || "Une erreur est survenue");
        } finally {
            setIsLoading(false);
        }
    };

    if (isSuccess) {
        return (
            <div className="min-h-screen bg-secondary/30 flex items-center justify-center p-4">
                <Card className="w-full max-w-md">
                    <CardHeader className="text-center">
                        <div className="w-16 h-16 bg-success/10 rounded-full flex items-center justify-center mx-auto mb-4">
                            <CheckCircleIcon className="h-8 w-8 text-success" />
                        </div>
                        <CardTitle className="text-2xl text-center">Email envoyé !</CardTitle>
                        <CardDescription className="text-center">
                            Vérifiez votre boîte de réception
                        </CardDescription>
                    </CardHeader>

                    <CardContent className="space-y-6">
                        <div className="text-center space-y-4">
                            <p className="text-muted-foreground">
                                Nous avons envoyé un lien de réinitialisation de mot de passe à :
                            </p>
                            <p className="font-semibold text-primary">{email}</p>
                            <p className="text-sm text-muted-foreground">
                                Si vous ne recevez pas l'email dans les prochaines minutes, vérifiez
                                vos spams ou réessayez.
                            </p>
                        </div>

                        <div className="space-y-3">
                            <Button
                                variant="primary"
                                fullWidth
                                onClick={() => {
                                    setIsSuccess(false);
                                    setEmail("");
                                }}
                            >
                                Renvoyer l'email
                            </Button>

                            <Button
                                variant="outline"
                                fullWidth
                                onClick={() => router.push("/auth/login")}
                                icon={<ArrowLeftIcon className="h-4 w-4" />}
                            >
                                Retour à la connexion
                            </Button>
                        </div>
                    </CardContent>
                </Card>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-secondary/30 flex items-center justify-center p-4">
            <Card className="w-full max-w-md">
                <CardHeader className="text-center">
                    <div className="w-16 h-16 civic-gradient rounded-full flex items-center justify-center mx-auto mb-4">
                        <EnvelopeIcon className="h-8 w-8 text-white" />
                    </div>
                    <CardTitle className="text-2xl text-center">Mot de passe oublié</CardTitle>
                    <CardDescription className="text-center">
                        Entrez votre adresse email pour recevoir un lien de réinitialisation
                    </CardDescription>
                </CardHeader>

                <CardContent>
                    <form onSubmit={handleSubmit} className="space-y-6">
                        <div className="space-y-2">
                            <label htmlFor="email" className="text-sm font-medium text-foreground">
                                Adresse email *
                            </label>
                            <Input
                                id="email"
                                type="email"
                                required
                                value={email}
                                onChange={(e) => setEmail(e.target.value)}
                                placeholder="<EMAIL>"
                                className="w-full"
                                disabled={isLoading}
                            />
                        </div>

                        {error && (
                            <div className="flex items-start gap-3 p-4 bg-destructive/10 border border-destructive/20 rounded-xl">
                                <ExclamationTriangleIcon className="h-5 w-5 text-destructive flex-shrink-0 mt-0.5" />
                                <p className="text-sm text-destructive">{error}</p>
                            </div>
                        )}

                        <div className="space-y-3">
                            <Button
                                type="submit"
                                variant="primary"
                                fullWidth
                                loading={isLoading}
                                disabled={!email.trim()}
                            >
                                Envoyer le lien de réinitialisation
                            </Button>

                            <Button
                                type="button"
                                variant="ghost"
                                fullWidth
                                onClick={() => router.push("/auth/login")}
                                icon={<ArrowLeftIcon className="h-4 w-4" />}
                                disabled={isLoading}
                            >
                                Retour à la connexion
                            </Button>
                        </div>
                    </form>

                    <div className="mt-8 pt-6 border-t border-gray-200">
                        <div className="text-center space-y-2">
                            <p className="text-sm text-muted-foreground">Pas encore de compte ?</p>
                            <Link
                                href="/auth/register"
                                className="text-sm font-medium text-primary hover:text-primary/80 transition-colors"
                            >
                                Créer un compte PillarScan
                            </Link>
                        </div>
                    </div>
                </CardContent>
            </Card>
        </div>
    );
}
