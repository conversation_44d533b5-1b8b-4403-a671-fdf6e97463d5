import Link from "next/link";
import React from "react";

export default function AuthLayout({ children }: { children: any }) {
    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
            {/* Header */}
            <header className="bg-white shadow-sm">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex justify-between items-center h-16">
                        <div className="flex items-center">
                            <div className="flex-shrink-0">
                                <Link href="/" className="flex items-center space-x-2">
                                    <h1 className="text-2xl font-bold text-blue-600">PillarScan</h1>
                                </Link>
                            </div>
                        </div>
                        <div className="text-sm text-gray-500">
                            La voix citoyenne qui transforme la France
                        </div>
                    </div>
                </div>
            </header>

            {/* Main Content */}
            <main>{children}</main>

            {/* Footer */}
            <footer className="bg-white border-t border-gray-200 mt-auto">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
                    <div className="text-center text-sm text-gray-500">
                        <p>
                            © 2024 PillarScan. Une initiative pour la démocratie participative
                            française.
                        </p>
                    </div>
                </div>
            </footer>
        </div>
    );
}
