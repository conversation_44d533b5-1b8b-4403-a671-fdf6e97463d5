"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { useAuthStore } from "@/stores/authStore";
import Button from "@/components/ui/Button";
import Input from "@/components/ui/Input";
import { Card } from "@/components/ui/Card";

export default function RegisterPage() {
    const router = useRouter();
    const { register, isLoading, error, clearError } = useAuthStore();

    const [formData, setFormData] = useState({
        username: "",
        email: "",
        password: "",
        confirmPassword: "",
        acceptTerms: false,
    });

    const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

    const validateForm = () => {
        const errors: Record<string, string> = {};

        if (formData.username.length < 3) {
            errors.username = "Le nom d'utilisateur doit contenir au moins 3 caractères";
        }

        if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
            errors.email = "Veuillez entrer une adresse email valide";
        }

        if (formData.password.length < 6) {
            errors.password = "Le mot de passe doit contenir au moins 6 caractères";
        }

        if (formData.password !== formData.confirmPassword) {
            errors.confirmPassword = "Les mots de passe ne correspondent pas";
        }

        if (!formData.acceptTerms) {
            errors.acceptTerms = "Vous devez accepter les conditions d'utilisation";
        }

        setValidationErrors(errors);
        return Object.keys(errors).length === 0;
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        clearError();

        if (!validateForm()) {
            return;
        }

        try {
            await register(formData.username, formData.email, formData.password);
            router.push("/dashboard");
        } catch (error) {
            // Error is handled by the store
        }
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value, type, checked } = e.target;
        setFormData((prev) => ({
            ...prev,
            [name]: type === "checkbox" ? checked : value,
        }));

        // Clear validation error when user starts typing
        if (validationErrors[name]) {
            setValidationErrors((prev) => ({
                ...prev,
                [name]: "",
            }));
        }
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
            <div className="w-full max-w-md">
                {/* Header */}
                <div className="text-center mb-8">
                    <h1 className="text-3xl font-bold text-gray-900 mb-2">Rejoignez PillarScan</h1>
                    <p className="text-gray-600">
                        Créez votre compte pour commencer à faire entendre votre voix
                    </p>
                </div>

                {/* Register Form */}
                <Card className="p-6">
                    <form onSubmit={handleSubmit} className="space-y-4">
                        <div>
                            <label
                                htmlFor="username"
                                className="block text-sm font-medium text-gray-700 mb-1"
                            >
                                Nom d'utilisateur
                            </label>
                            <Input
                                id="username"
                                name="username"
                                type="text"
                                required
                                value={formData.username}
                                onChange={handleChange}
                                placeholder="votre_nom"
                                className="w-full"
                            />
                            {validationErrors.username && (
                                <p className="text-sm text-red-600 mt-1">
                                    {validationErrors.username}
                                </p>
                            )}
                        </div>

                        <div>
                            <label
                                htmlFor="email"
                                className="block text-sm font-medium text-gray-700 mb-1"
                            >
                                Adresse email
                            </label>
                            <Input
                                id="email"
                                name="email"
                                type="email"
                                required
                                value={formData.email}
                                onChange={handleChange}
                                placeholder="<EMAIL>"
                                className="w-full"
                            />
                            {validationErrors.email && (
                                <p className="text-sm text-red-600 mt-1">
                                    {validationErrors.email}
                                </p>
                            )}
                        </div>

                        <div>
                            <label
                                htmlFor="password"
                                className="block text-sm font-medium text-gray-700 mb-1"
                            >
                                Mot de passe
                            </label>
                            <Input
                                id="password"
                                name="password"
                                type="password"
                                required
                                value={formData.password}
                                onChange={handleChange}
                                placeholder="••••••••"
                                className="w-full"
                            />
                            {validationErrors.password && (
                                <p className="text-sm text-red-600 mt-1">
                                    {validationErrors.password}
                                </p>
                            )}
                        </div>

                        <div>
                            <label
                                htmlFor="confirmPassword"
                                className="block text-sm font-medium text-gray-700 mb-1"
                            >
                                Confirmer le mot de passe
                            </label>
                            <Input
                                id="confirmPassword"
                                name="confirmPassword"
                                type="password"
                                required
                                value={formData.confirmPassword}
                                onChange={handleChange}
                                placeholder="••••••••"
                                className="w-full"
                            />
                            {validationErrors.confirmPassword && (
                                <p className="text-sm text-red-600 mt-1">
                                    {validationErrors.confirmPassword}
                                </p>
                            )}
                        </div>

                        <div className="flex items-start">
                            <input
                                id="acceptTerms"
                                name="acceptTerms"
                                type="checkbox"
                                checked={formData.acceptTerms}
                                onChange={handleChange}
                                className="mt-1 h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                            />
                            <label htmlFor="acceptTerms" className="ml-2 text-sm text-gray-700">
                                J'accepte les{" "}
                                <Link
                                    href="/legal/terms"
                                    className="text-blue-600 hover:text-blue-500"
                                >
                                    conditions d'utilisation
                                </Link>{" "}
                                et la{" "}
                                <Link
                                    href="/legal/privacy"
                                    className="text-blue-600 hover:text-blue-500"
                                >
                                    politique de confidentialité
                                </Link>
                            </label>
                        </div>
                        {validationErrors.acceptTerms && (
                            <p className="text-sm text-red-600">{validationErrors.acceptTerms}</p>
                        )}

                        {error && (
                            <div className="bg-red-50 border border-red-200 rounded-md p-3">
                                <p className="text-sm text-red-600">{error}</p>
                            </div>
                        )}

                        <Button
                            type="submit"
                            loading={isLoading}
                            className="w-full bg-blue-600 hover:bg-blue-700 text-white"
                        >
                            Créer mon compte
                        </Button>
                    </form>
                </Card>

                {/* Login Link */}
                <div className="mt-6 text-center">
                    <p className="text-sm text-gray-600">
                        Déjà un compte ?{" "}
                        <Link
                            href="/auth/login"
                            className="font-medium text-blue-600 hover:text-blue-500"
                        >
                            Se connecter
                        </Link>
                    </p>
                </div>
            </div>
        </div>
    );
}
