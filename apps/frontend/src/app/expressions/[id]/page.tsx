"use client";

import React, { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import Link from "next/link";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import Badge from "@/components/ui/Badge";
import { expressionsApi } from "@/lib/api";
import {
    ArrowLeftIcon,
    MapPinIcon,
    CalendarIcon,
    UserIcon,
    HeartIcon,
    ShareIcon,
    ChatBubbleLeftRightIcon,
    ExclamationTriangleIcon,
    CheckCircleIcon,
    ClockIcon,
} from "@heroicons/react/24/outline";
import { HeartIcon as HeartIconSolid } from "@heroicons/react/24/solid";

interface Expression {
    id: string;
    titre: string;
    contenu: string;
    type_expression: string;
    urgence: number;
    etat_emotionnel: string;
    statut: string;
    date_creation: string;
    date_evenement: string;
    auteur?: {
        nom?: string;
        username?: string;
    };
    piliers?: Array<{
        id: string;
        nom: string;
        couleur: string;
        code: string;
    }>;
    lieu?: {
        nom: string;
        coordonnees?: {
            lat: number;
            lng: number;
        };
    };
}

const getStatusColor = (status: string) => {
    switch (status) {
        case "brouillon":
            return "secondary";
        case "en_moderation":
            return "warning";
        case "publie":
            return "info";
        case "en_traitement":
            return "primary";
        case "resolu":
            return "success";
        case "rejete":
            return "destructive";
        default:
            return "secondary";
    }
};

const getStatusLabel = (status: string) => {
    switch (status) {
        case "brouillon":
            return "Brouillon";
        case "en_moderation":
            return "En modération";
        case "publie":
            return "Publiée";
        case "en_traitement":
            return "En traitement";
        case "resolu":
            return "Résolue";
        case "rejete":
            return "Rejetée";
        default:
            return status;
    }
};

const getUrgencyColor = (urgence: number) => {
    if (urgence >= 4) return "destructive";
    if (urgence >= 3) return "warning";
    return "info";
};

const getTypeLabel = (type: string) => {
    switch (type) {
        case "probleme":
            return "Problème";
        case "satisfaction":
            return "Satisfaction";
        case "idee":
            return "Idée";
        case "question":
            return "Question";
        default:
            return type;
    }
};

export default function ExpressionDetailPage() {
    const params = useParams();
    const router = useRouter();
    const expressionId = params.id as string;

    const [expression, setExpression] = useState<Expression | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [liked, setLiked] = useState(false);
    const [likeCount, setLikeCount] = useState(0);

    useEffect(() => {
        if (expressionId) {
            loadExpression();
        }
    }, [expressionId]);

    const loadExpression = async () => {
        try {
            setLoading(true);
            const response = await expressionsApi.getById(expressionId);
            setExpression(response.data);
            setLikeCount(Math.floor(Math.random() * 50) + 5); // Mock data
        } catch (error: any) {
            setError(error.message || "Erreur lors du chargement de l'expression");
        } finally {
            setLoading(false);
        }
    };

    const handleLike = () => {
        setLiked(!liked);
        setLikeCount((prev) => (liked ? prev - 1 : prev + 1));
    };

    const handleShare = () => {
        if (navigator.share) {
            navigator.share({
                title: expression?.titre,
                text: expression?.contenu.substring(0, 100) + "...",
                url: window.location.href,
            });
        } else {
            navigator.clipboard.writeText(window.location.href);
            // You could show a toast notification here
        }
    };

    if (loading) {
        return (
            <div className="min-h-screen bg-secondary/30 flex items-center justify-center">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
                    <p className="text-muted-foreground">Chargement de l'expression...</p>
                </div>
            </div>
        );
    }

    if (error || !expression) {
        return (
            <div className="min-h-screen bg-secondary/30 flex items-center justify-center">
                <Card className="max-w-md mx-auto p-8 text-center">
                    <ExclamationTriangleIcon className="h-12 w-12 text-destructive mx-auto mb-4" />
                    <h2 className="text-xl font-bold mb-4">Expression introuvable</h2>
                    <p className="text-muted-foreground mb-6">
                        {error || "Cette expression n'existe pas ou a été supprimée."}
                    </p>
                    <Button variant="primary" onClick={() => router.back()}>
                        Retour
                    </Button>
                </Card>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-secondary/30">
            {/* Header */}
            <section className="bg-background border-b">
                <div className="civic-container py-6">
                    <div className="flex items-center justify-between">
                        <Button
                            variant="ghost"
                            onClick={() => router.back()}
                            icon={<ArrowLeftIcon className="h-4 w-4" />}
                        >
                            Retour
                        </Button>
                        <div className="flex items-center gap-3">
                            <Button
                                variant="ghost"
                                size="sm"
                                onClick={handleLike}
                                icon={
                                    liked ? (
                                        <HeartIconSolid className="h-4 w-4 text-red-500" />
                                    ) : (
                                        <HeartIcon className="h-4 w-4" />
                                    )
                                }
                            >
                                {likeCount}
                            </Button>
                            <Button
                                variant="ghost"
                                size="sm"
                                onClick={handleShare}
                                icon={<ShareIcon className="h-4 w-4" />}
                            >
                                Partager
                            </Button>
                        </div>
                    </div>
                </div>
            </section>

            <div className="civic-container py-8">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    {/* Main Content */}
                    <div className="lg:col-span-2 space-y-6">
                        {/* Expression Header */}
                        <Card className="p-8">
                            <div className="flex flex-wrap items-center gap-3 mb-6">
                                <Badge variant={getStatusColor(expression.statut as any)}>
                                    {getStatusLabel(expression.statut)}
                                </Badge>
                                <Badge variant="outline">
                                    {getTypeLabel(expression.type_expression)}
                                </Badge>
                                <Badge variant={getUrgencyColor(expression.urgence)}>
                                    Urgence {expression.urgence}/5
                                </Badge>
                            </div>

                            <h1 className="text-3xl md:text-4xl font-bold mb-6 text-foreground leading-tight">
                                {expression.titre}
                            </h1>

                            <div className="flex flex-wrap items-center gap-6 text-sm text-muted-foreground mb-8">
                                <div className="flex items-center gap-2">
                                    <UserIcon className="h-4 w-4" />
                                    <span>
                                        {expression.auteur?.nom ||
                                            expression.auteur?.username ||
                                            "Citoyen anonyme"}
                                    </span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <CalendarIcon className="h-4 w-4" />
                                    <span>
                                        {new Date(expression.date_creation).toLocaleDateString(
                                            "fr-FR",
                                        )}
                                    </span>
                                </div>
                                {expression.lieu && (
                                    <div className="flex items-center gap-2">
                                        <MapPinIcon className="h-4 w-4" />
                                        <span>{expression.lieu.nom}</span>
                                    </div>
                                )}
                            </div>

                            {/* Piliers */}
                            {expression.piliers && expression.piliers.length > 0 && (
                                <div className="mb-8">
                                    <h3 className="text-sm font-semibold text-muted-foreground mb-3">
                                        Piliers concernés
                                    </h3>
                                    <div className="flex flex-wrap gap-2">
                                        {expression.piliers.map((pilier) => (
                                            <Badge
                                                key={pilier.id}
                                                variant="outline"
                                                className="border-2"
                                                style={{
                                                    borderColor: pilier.couleur,
                                                    color: pilier.couleur,
                                                }}
                                            >
                                                {pilier.nom}
                                            </Badge>
                                        ))}
                                    </div>
                                </div>
                            )}

                            {/* Content */}
                            <div className="prose prose-lg max-w-none">
                                <p className="text-foreground leading-relaxed whitespace-pre-wrap">
                                    {expression.contenu}
                                </p>
                            </div>
                        </Card>

                        {/* Timeline / Actions */}
                        <Card className="p-8">
                            <h2 className="text-2xl font-bold mb-6 text-foreground">
                                Suivi et actions
                            </h2>

                            <div className="space-y-6">
                                {/* Mock timeline */}
                                <div className="flex items-start gap-4">
                                    <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center flex-shrink-0">
                                        <ChatBubbleLeftRightIcon className="h-4 w-4 text-white" />
                                    </div>
                                    <div>
                                        <div className="font-semibold text-foreground">
                                            Expression soumise
                                        </div>
                                        <div className="text-sm text-muted-foreground">
                                            {new Date(expression.date_creation).toLocaleDateString(
                                                "fr-FR",
                                            )}{" "}
                                            à{" "}
                                            {new Date(expression.date_creation).toLocaleTimeString(
                                                "fr-FR",
                                            )}
                                        </div>
                                    </div>
                                </div>

                                {expression.statut !== "brouillon" && (
                                    <div className="flex items-start gap-4">
                                        <div className="w-8 h-8 bg-warning rounded-full flex items-center justify-center flex-shrink-0">
                                            <ClockIcon className="h-4 w-4 text-white" />
                                        </div>
                                        <div>
                                            <div className="font-semibold text-foreground">
                                                Modération en cours
                                            </div>
                                            <div className="text-sm text-muted-foreground">
                                                Vérification par nos validateurs
                                            </div>
                                        </div>
                                    </div>
                                )}

                                {expression.statut === "resolu" && (
                                    <div className="flex items-start gap-4">
                                        <div className="w-8 h-8 bg-success rounded-full flex items-center justify-center flex-shrink-0">
                                            <CheckCircleIcon className="h-4 w-4 text-white" />
                                        </div>
                                        <div>
                                            <div className="font-semibold text-foreground">
                                                Problème résolu
                                            </div>
                                            <div className="text-sm text-muted-foreground">
                                                Les actions ont été mises en place
                                            </div>
                                        </div>
                                    </div>
                                )}
                            </div>
                        </Card>
                    </div>

                    {/* Sidebar */}
                    <div className="space-y-6">
                        {/* Quick Actions */}
                        <Card className="p-6">
                            <CardTitle className="mb-4">Actions rapides</CardTitle>
                            <div className="space-y-3">
                                <Button variant="primary" size="sm" fullWidth>
                                    Soutenir cette expression
                                </Button>
                                <Button variant="outline" size="sm" fullWidth>
                                    Signaler un problème similaire
                                </Button>
                                <Button variant="ghost" size="sm" fullWidth>
                                    Contacter l'auteur
                                </Button>
                            </div>
                        </Card>

                        {/* Statistics */}
                        <Card className="p-6">
                            <CardTitle className="mb-4">Statistiques</CardTitle>
                            <div className="space-y-4">
                                <div className="flex justify-between">
                                    <span className="text-muted-foreground">Vues</span>
                                    <span className="font-semibold">
                                        {Math.floor(Math.random() * 200) + 50}
                                    </span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-muted-foreground">Soutiens</span>
                                    <span className="font-semibold">{likeCount}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-muted-foreground">Partages</span>
                                    <span className="font-semibold">
                                        {Math.floor(Math.random() * 15) + 2}
                                    </span>
                                </div>
                            </div>
                        </Card>

                        {/* Related */}
                        <Card className="p-6">
                            <CardTitle className="mb-4">Expressions similaires</CardTitle>
                            <div className="space-y-3">
                                <div className="text-sm">
                                    <div className="font-medium text-foreground">
                                        Problème de transport ligne 13
                                    </div>
                                    <div className="text-muted-foreground">3 soutiens</div>
                                </div>
                                <div className="text-sm">
                                    <div className="font-medium text-foreground">
                                        Retards récurrents RER B
                                    </div>
                                    <div className="text-muted-foreground">8 soutiens</div>
                                </div>
                                <div className="text-sm">
                                    <div className="font-medium text-foreground">
                                        Amélioration signalétique métro
                                    </div>
                                    <div className="text-muted-foreground">12 soutiens</div>
                                </div>
                            </div>
                            <Button variant="outline" size="sm" fullWidth className="mt-4">
                                Voir toutes
                            </Button>
                        </Card>
                    </div>
                </div>
            </div>
        </div>
    );
}
