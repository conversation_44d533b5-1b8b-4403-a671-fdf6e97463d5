"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuthStore } from "@/stores/authStore";
import Button from "@/components/ui/Button";
import Input from "@/components/ui/Input";
import { Card } from "@/components/ui/Card";
import { expressionsApi, piliersApi } from "@/lib/api";
import FileUpload, { UploadedFile } from "@/components/forms/FileUpload";
import LocationPicker from "@/components/maps/LocationPicker";
import { Lieu } from "@/types";

interface Pilier {
    id: string;
    code: string;
    nom: string;
    couleur: string;
    icone: string;
}

export default function NewExpressionPage() {
    const router = useRouter();
    const { isAuthenticated, user } = useAuthStore();

    const [piliers, setPiliers] = useState<Pilier[]>([]);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const [formData, setFormData] = useState({
        titre: "",
        contenu: "",
        type_expression: "probleme" as "probleme" | "satisfaction" | "idee" | "question",
        urgence: 3 as 1 | 2 | 3 | 4 | 5,
        etat_emotionnel: "neutre" as
            | "colere"
            | "joie"
            | "tristesse"
            | "espoir"
            | "neutre"
            | "frustration",
        date_evenement: new Date().toISOString().split("T")[0],
        piliers: [] as string[],
        lieu: null as Lieu | null,
        tags: [] as string[],
    });

    const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);

    useEffect(() => {
        if (!isAuthenticated) {
            router.push("/auth/login");
            return;
        }

        loadPiliers();
    }, [isAuthenticated, router]);

    const loadPiliers = async () => {
        try {
            const response = await piliersApi.getAll();
            setPiliers((response.data as any) || []);
        } catch (error) {
            console.error("Erreur lors du chargement des piliers:", error);
        }
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setIsLoading(true);
        setError(null);

        try {
            // Validation
            if (!formData.titre.trim()) {
                throw new Error("Le titre est requis");
            }
            if (!formData.contenu.trim()) {
                throw new Error("Le contenu est requis");
            }
            if (formData.piliers.length === 0) {
                throw new Error("Veuillez sélectionner au moins un pilier");
            }

            const expressionData = {
                ...formData,
                auteur: user?.id,
                statut: "brouillon",
                source: "web",
                langue: "fr",
                version: 1,
                lieu: formData.lieu?.id || null, // Send only the lieu ID, not the full object
                medias: uploadedFiles.filter((f) => f.uploaded).map((f) => f.url),
            };

            await expressionsApi.create(expressionData);

            // Redirect to expressions list
            router.push("/expressions?success=created");
        } catch (error: any) {
            setError(error.message || "Erreur lors de la création de l'expression");
        } finally {
            setIsLoading(false);
        }
    };

    const handleChange = (
        e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>,
    ) => {
        const { name, value } = e.target;
        setFormData((prev) => ({
            ...prev,
            [name]: value,
        }));
    };

    const handlePilierToggle = (pilierId: string) => {
        setFormData((prev) => ({
            ...prev,
            piliers: prev.piliers.includes(pilierId)
                ? prev.piliers.filter((id) => id !== pilierId)
                : [...prev.piliers, pilierId],
        }));
    };

    if (!isAuthenticated) {
        return null;
    }

    return (
        <div className="min-h-screen bg-gray-50">
            {/* Header */}
            <header className="bg-white shadow">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex justify-between items-center h-16">
                        <div className="flex items-center">
                            <button
                                onClick={() => router.back()}
                                className="mr-4 p-2 rounded-md hover:bg-gray-100"
                            >
                                <svg
                                    className="w-5 h-5"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                >
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M15 19l-7-7 7-7"
                                    />
                                </svg>
                            </button>
                            <h1 className="text-2xl font-bold text-blue-600">PillarScan</h1>
                            <span className="ml-4 text-gray-500">Nouvelle expression</span>
                        </div>
                    </div>
                </div>
            </header>

            {/* Main Content */}
            <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                <div className="mb-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-2">
                        Exprimer une préoccupation citoyenne
                    </h2>
                    <p className="text-gray-600">
                        Partagez votre expérience pour contribuer à l'amélioration de la société
                        française.
                    </p>
                </div>

                <form onSubmit={handleSubmit} className="space-y-6">
                    {/* Basic Information */}
                    <Card className="p-6">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">
                            Informations générales
                        </h3>

                        <div className="space-y-4">
                            <div>
                                <label
                                    htmlFor="titre"
                                    className="block text-sm font-medium text-gray-700 mb-1"
                                >
                                    Titre de votre expression *
                                </label>
                                <Input
                                    id="titre"
                                    name="titre"
                                    type="text"
                                    required
                                    value={formData.titre}
                                    onChange={handleChange}
                                    placeholder="Résumez votre préoccupation en quelques mots"
                                    className="w-full"
                                />
                            </div>

                            <div>
                                <label
                                    htmlFor="contenu"
                                    className="block text-sm font-medium text-gray-700 mb-1"
                                >
                                    Description détaillée *
                                </label>
                                <textarea
                                    id="contenu"
                                    name="contenu"
                                    required
                                    value={formData.contenu}
                                    onChange={handleChange}
                                    placeholder="Décrivez votre expérience, le problème rencontré, ou votre idée d'amélioration..."
                                    rows={6}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                />
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label
                                        htmlFor="type_expression"
                                        className="block text-sm font-medium text-gray-700 mb-1"
                                    >
                                        Type d'expression
                                    </label>
                                    <select
                                        id="type_expression"
                                        name="type_expression"
                                        value={formData.type_expression}
                                        onChange={handleChange}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    >
                                        <option value="probleme">Problème</option>
                                        <option value="satisfaction">Satisfaction</option>
                                        <option value="idee">Idée d'amélioration</option>
                                        <option value="question">Question</option>
                                    </select>
                                </div>

                                <div>
                                    <label
                                        htmlFor="urgence"
                                        className="block text-sm font-medium text-gray-700 mb-1"
                                    >
                                        Niveau d'urgence
                                    </label>
                                    <select
                                        id="urgence"
                                        name="urgence"
                                        value={formData.urgence}
                                        onChange={handleChange}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    >
                                        <option value={1}>1 - Très faible</option>
                                        <option value={2}>2 - Faible</option>
                                        <option value={3}>3 - Modérée</option>
                                        <option value={4}>4 - Élevée</option>
                                        <option value={5}>5 - Critique</option>
                                    </select>
                                </div>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label
                                        htmlFor="date_evenement"
                                        className="block text-sm font-medium text-gray-700 mb-1"
                                    >
                                        Date de l'événement
                                    </label>
                                    <Input
                                        id="date_evenement"
                                        name="date_evenement"
                                        type="date"
                                        value={formData.date_evenement}
                                        onChange={handleChange}
                                        className="w-full"
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Lieu de l'événement
                                    </label>
                                    <LocationPicker
                                        onLocationSelect={(location) => {
                                            setFormData((prev) => ({
                                                ...prev,
                                                lieu: location
                                                    ? ({
                                                          id: location.id,
                                                          nom: location.name,
                                                          type: "adresse" as const,
                                                          niveau: "ville" as const,
                                                          coordonnees: {
                                                              lat: location.coordinates.lat,
                                                              lng: location.coordinates.lng,
                                                          },
                                                          adresse_complete: location.fullName,
                                                          pays: "France",
                                                          actif: true,
                                                          verifie: true,
                                                      } as Lieu)
                                                    : null,
                                            }));
                                        }}
                                        placeholder="Sélectionnez un lieu..."
                                    />
                                </div>
                            </div>
                        </div>
                    </Card>

                    {/* Piliers Selection */}
                    <Card className="p-6">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">
                            Piliers concernés *
                        </h3>
                        <p className="text-sm text-gray-600 mb-4">
                            Sélectionnez les domaines de la société française concernés par votre
                            expression.
                        </p>

                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                            {piliers.map((pilier) => (
                                <button
                                    key={pilier.id}
                                    type="button"
                                    onClick={() => handlePilierToggle(pilier.id)}
                                    className={`p-3 rounded-lg border-2 text-left transition-colors ${
                                        formData.piliers.includes(pilier.id)
                                            ? "border-blue-500 bg-blue-50"
                                            : "border-gray-200 hover:border-gray-300"
                                    }`}
                                >
                                    <div className="flex items-center">
                                        <div
                                            className="w-4 h-4 rounded-full mr-3"
                                            style={{ backgroundColor: pilier.couleur }}
                                        />
                                        <span className="font-medium text-sm">{pilier.nom}</span>
                                    </div>
                                </button>
                            ))}
                        </div>
                    </Card>

                    {/* File Upload */}
                    <Card className="p-6">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">
                            Pièces justificatives (optionnel)
                        </h3>
                        <p className="text-sm text-gray-600 mb-4">
                            Ajoutez des photos, documents ou vidéos pour appuyer votre expression.
                        </p>

                        <FileUpload
                            onFilesChange={setUploadedFiles}
                            maxFiles={5}
                            maxSize={10}
                            acceptedTypes={[
                                "image/*",
                                "application/pdf",
                                ".doc",
                                ".docx",
                                "video/mp4",
                                "video/webm",
                            ]}
                        />
                    </Card>

                    {/* Error Display */}
                    {error && (
                        <div className="bg-red-50 border border-red-200 rounded-md p-4">
                            <p className="text-sm text-red-600">{error}</p>
                        </div>
                    )}

                    {/* Submit Button */}
                    <div className="flex justify-end space-x-4">
                        <Button type="button" variant="outline" onClick={() => router.back()}>
                            Annuler
                        </Button>
                        <Button
                            type="submit"
                            loading={isLoading}
                            className="bg-blue-600 hover:bg-blue-700 text-white"
                        >
                            Soumettre l'expression
                        </Button>
                    </div>
                </form>
            </main>
        </div>
    );
}
