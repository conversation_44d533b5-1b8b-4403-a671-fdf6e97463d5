"use client";

import React, { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { expressionsApi, piliersApi } from "@/lib/api";
import Button from "@/components/ui/Button";
import { Card } from "@/components/ui/Card";
import Badge from "@/components/ui/Badge";
import SearchBar from "@/components/search/SearchBar";
import AdvancedFilters, { FilterOptions } from "@/components/filters/AdvancedFilters";
import useSearch from "@/hooks/useSearch";

// Types for French civic expressions
interface Pilier {
    id: string;
    code: string;
    nom: string;
    description: string;
    icone: string;
    couleur: string;
}

interface Expression {
    id: string;
    titre: string;
    contenu: string;
    type_expression: "probleme" | "satisfaction" | "idee" | "question";
    urgence: 1 | 2 | 3 | 4 | 5;
    etat_emotionnel: "colere" | "joie" | "tristesse" | "espoir" | "neutre" | "frustration";
    statut: "brouillon" | "en_moderation" | "publie" | "en_traitement" | "resolu" | "rejete";
    auteur: string;
    date_creation: string;
    date_evenement: string;
    lieu: string;
    piliers: Pilier[];
    impact: {
        vues: number;
        soutiens: number;
        partages: number;
        commentaires: number;
    };
}

export default function ExpressionsPage() {
    const router = useRouter();
    const searchParams = useSearchParams();
    const [expressions, setExpressions] = useState<Expression[]>([]);
    const [piliers, setPiliers] = useState<Pilier[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [showFilters, setShowFilters] = useState(false);

    const {
        query,
        filters,
        updateQuery,
        updateFilters,
        clearSearch,
        getActiveFilterCount,
        searchHistory,
    } = useSearch({
        onSearch: (searchQuery, searchFilters) => {
            loadData(searchQuery, searchFilters);
        },
    });

    useEffect(() => {
        loadData();

        // Check for success message
        if (searchParams.get("success") === "created") {
            // Show success message (you could use a toast library here)
            console.log("Expression créée avec succès !");
        }
    }, [searchParams]);

    const loadData = async (searchQuery?: string, searchFilters?: FilterOptions) => {
        setIsLoading(true);
        try {
            // Build API parameters based on search and filters
            const params: any = { populate: "piliers" };

            if (searchQuery) {
                params.filters = {
                    $or: [
                        { titre: { $containsi: searchQuery } },
                        { contenu: { $containsi: searchQuery } },
                    ],
                };
            }

            if (searchFilters?.piliers?.length) {
                params.filters = {
                    ...params.filters,
                    piliers: { id: { $in: searchFilters.piliers } },
                };
            }

            if (searchFilters?.typeExpression?.length) {
                params.filters = {
                    ...params.filters,
                    type_expression: { $in: searchFilters.typeExpression },
                };
            }

            if (searchFilters?.urgence?.length) {
                params.filters = {
                    ...params.filters,
                    urgence: { $in: searchFilters.urgence },
                };
            }

            if (searchFilters?.statut?.length) {
                params.filters = {
                    ...params.filters,
                    statut: { $in: searchFilters.statut },
                };
            }

            // Load expressions and piliers in parallel
            const [expressionsResponse, piliersResponse] = await Promise.all([
                expressionsApi.getPublic(params),
                piliersApi.getAll(),
            ]);

            setExpressions((expressionsResponse.data as any) || []);
            setPiliers((piliersResponse.data as any) || []);
        } catch (error) {
            console.error("Erreur lors du chargement des données:", error);
            // Fallback to mock data if API fails
            setExpressions(mockExpressions);
            setPiliers(mockPiliers);
        } finally {
            setIsLoading(false);
        }
    };

    const getTypeLabel = (type: string) => {
        const labels = {
            probleme: "Problème",
            satisfaction: "Satisfaction",
            idee: "Idée",
            question: "Question",
        };
        return labels[type as keyof typeof labels] || type;
    };

    const getTypeColor = (type: string) => {
        const colors = {
            probleme: "bg-red-100 text-red-800",
            satisfaction: "bg-green-100 text-green-800",
            idee: "bg-blue-100 text-blue-800",
            question: "bg-yellow-100 text-yellow-800",
        };
        return colors[type as keyof typeof colors] || "bg-gray-100 text-gray-800";
    };

    const getUrgenceLabel = (urgence: number) => {
        const labels = {
            1: "Très faible",
            2: "Faible",
            3: "Modérée",
            4: "Élevée",
            5: "Critique",
        };
        return labels[urgence as keyof typeof labels] || "Inconnue";
    };

    const getUrgenceColor = (urgence: number) => {
        const colors = {
            1: "bg-gray-100 text-gray-800",
            2: "bg-blue-100 text-blue-800",
            3: "bg-yellow-100 text-yellow-800",
            4: "bg-orange-100 text-orange-800",
            5: "bg-red-100 text-red-800",
        };
        return colors[urgence as keyof typeof colors] || "bg-gray-100 text-gray-800";
    };

    // No need for client-side filtering since we filter on the server
    const filteredExpressions = expressions;

    if (isLoading) {
        return (
            <div className="min-h-screen bg-gray-50 flex items-center justify-center">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                    <p className="mt-4 text-gray-600">Chargement des expressions...</p>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-50">
            {/* Header */}
            <header className="bg-white shadow">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex justify-between items-center h-16">
                        <div className="flex items-center">
                            <h1 className="text-2xl font-bold text-blue-600">PillarScan</h1>
                            <span className="ml-4 text-gray-500">Expressions citoyennes</span>
                        </div>
                        <div className="flex items-center space-x-4">
                            <Button
                                onClick={() => router.push("/expressions/new")}
                                className="bg-blue-600 hover:bg-blue-700 text-white"
                            >
                                <svg
                                    className="w-5 h-5 mr-2"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                >
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M12 4v16m8-8H4"
                                    />
                                </svg>
                                Nouvelle expression
                            </Button>
                            <Button variant="outline" onClick={() => router.push("/dashboard")}>
                                Dashboard
                            </Button>
                        </div>
                    </div>
                </div>
            </header>

            {/* Main Content */}
            <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                {/* Page Header */}
                <div className="mb-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-2">
                        Expressions citoyennes
                    </h2>
                    <p className="text-gray-600">
                        Découvrez les préoccupations et idées partagées par les citoyens français
                        sur les 12 piliers de la société.
                    </p>
                </div>

                {/* Search and Filters */}
                <div className="mb-6 space-y-4">
                    <SearchBar
                        defaultValue={query}
                        onSearch={updateQuery}
                        onClear={clearSearch}
                        onFilterClick={() => setShowFilters(true)}
                        filterCount={getActiveFilterCount()}
                        recentSearches={searchHistory}
                        suggestions={[
                            "transport en retard",
                            "problème santé",
                            "éducation école",
                            "sécurité quartier",
                            "environnement pollution",
                        ]}
                    />

                    {/* Active Filters Display */}
                    {getActiveFilterCount() > 0 && (
                        <div className="flex flex-wrap gap-2">
                            {filters.piliers?.map((pilierId) => {
                                const pilier = piliers.find((p) => p.id === pilierId);
                                return pilier ? (
                                    <Badge
                                        key={pilierId}
                                        variant="secondary"
                                        className="cursor-pointer"
                                        onClick={() => {
                                            updateFilters({
                                                ...filters,
                                                piliers: filters.piliers?.filter(
                                                    (id) => id !== pilierId,
                                                ),
                                            });
                                        }}
                                    >
                                        {pilier.nom} ×
                                    </Badge>
                                ) : null;
                            })}

                            {filters.typeExpression?.map((type) => (
                                <Badge
                                    key={type}
                                    variant="secondary"
                                    className="cursor-pointer"
                                    onClick={() => {
                                        updateFilters({
                                            ...filters,
                                            typeExpression: filters.typeExpression?.filter(
                                                (t) => t !== type,
                                            ),
                                        });
                                    }}
                                >
                                    {getTypeLabel(type)} ×
                                </Badge>
                            ))}
                        </div>
                    )}
                </div>

                {/* Results Count */}
                <div className="mb-6">
                    <p className="text-sm text-gray-600">
                        {filteredExpressions.length} expression
                        {filteredExpressions.length !== 1 ? "s" : ""} trouvée
                        {filteredExpressions.length !== 1 ? "s" : ""}
                    </p>
                </div>

                {/* Expressions List */}
                <div className="space-y-6">
                    {filteredExpressions.length === 0 ? (
                        <Card className="p-8 text-center">
                            <p className="text-gray-500 mb-4">
                                Aucune expression trouvée avec ces filtres.
                            </p>
                            <Button
                                onClick={() => router.push("/expressions/new")}
                                className="bg-blue-600 hover:bg-blue-700 text-white"
                            >
                                Créer la première expression
                            </Button>
                        </Card>
                    ) : (
                        filteredExpressions.map((expression) => (
                            <Card
                                key={expression.id}
                                className="p-6 hover:shadow-lg transition-shadow"
                            >
                                <div className="flex justify-between items-start mb-4">
                                    <div className="flex-1">
                                        <h3 className="text-xl font-semibold text-gray-900 mb-2">
                                            {expression.titre}
                                        </h3>
                                        <p className="text-gray-600 mb-4 line-clamp-3">
                                            {expression.contenu}
                                        </p>
                                    </div>
                                </div>

                                {/* Badges */}
                                <div className="flex flex-wrap gap-2 mb-4">
                                    <Badge className={getTypeColor(expression.type_expression)}>
                                        {getTypeLabel(expression.type_expression)}
                                    </Badge>
                                    <Badge className={getUrgenceColor(expression.urgence)}>
                                        Urgence: {getUrgenceLabel(expression.urgence)}
                                    </Badge>
                                    {expression.piliers.map((pilier) => (
                                        <Badge
                                            key={pilier.id}
                                            className="text-white"
                                            style={{ backgroundColor: pilier.couleur }}
                                        >
                                            {pilier.nom}
                                        </Badge>
                                    ))}
                                </div>

                                {/* Meta Information */}
                                <div className="flex justify-between items-center text-sm text-gray-500">
                                    <div className="flex items-center space-x-4">
                                        <span>📍 {expression.lieu}</span>
                                        <span>
                                            📅{" "}
                                            {new Date(expression.date_creation).toLocaleDateString(
                                                "fr-FR",
                                            )}
                                        </span>
                                    </div>
                                    <div className="flex items-center space-x-4">
                                        <span>👁️ {expression.impact.vues}</span>
                                        <span>👍 {expression.impact.soutiens}</span>
                                        <span>💬 {expression.impact.commentaires}</span>
                                    </div>
                                </div>
                            </Card>
                        ))
                    )}
                </div>
            </main>

            {/* Advanced Filters Modal */}
            <AdvancedFilters
                isOpen={showFilters}
                onClose={() => setShowFilters(false)}
                filters={filters}
                onFiltersChange={updateFilters}
                onApply={() => setShowFilters(false)}
                onReset={() => {
                    updateFilters({});
                    setShowFilters(false);
                }}
                availableOptions={{
                    piliers: piliers.map((p) => ({ id: p.id, nom: p.nom, couleur: p.couleur })),
                }}
            />
        </div>
    );
}

// Mock data for fallback
const mockPiliers: Pilier[] = [
    {
        id: "1",
        code: "sante",
        nom: "Santé",
        description: "Système de santé",
        icone: "health",
        couleur: "#E74C3C",
    },
    {
        id: "2",
        code: "education",
        nom: "Éducation",
        description: "Système éducatif",
        icone: "school",
        couleur: "#3498DB",
    },
    {
        id: "3",
        code: "transport",
        nom: "Transport",
        description: "Mobilité et transport",
        icone: "transit",
        couleur: "#9B59B6",
    },
];

const mockExpressions: Expression[] = [
    {
        id: "1",
        titre: "Problème de transport en commun à Lyon",
        contenu: "Les bus sont souvent en retard et bondés aux heures de pointe...",
        type_expression: "probleme",
        urgence: 3,
        etat_emotionnel: "frustration",
        statut: "publie",
        auteur: "citoyen1",
        date_creation: "2024-01-15",
        date_evenement: "2024-01-14",
        lieu: "Lyon, Rhône-Alpes",
        piliers: [mockPiliers[2]],
        impact: { vues: 156, soutiens: 23, partages: 5, commentaires: 8 },
    },
];
