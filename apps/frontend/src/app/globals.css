@import "tailwindcss";

/* French Civic Design System - PillarScan */
:root {
  /* French Republic Colors */
  --fr-blue: #000091;      /* <PERSON> */
  --fr-white: #ffffff;     /* Pure White */
  --fr-red: #e1000f;       /* <PERSON> Red */
  
  /* Extended Civic Palette */
  --civic-navy: #1e293b;   /* Deep Navy */
  --civic-slate: #334155;  /* Slate */
  --civic-gray: #64748b;   /* Neutral Gray */
  --civic-light: #f8fafc;  /* Light Background */
  --civic-success: #059669; /* Success Green */
  --civic-warning: #d97706; /* Warning Orange */
  --civic-info: #0284c7;   /* Info Blue */
  
  /* Semantic Design Tokens */
  --background: var(--fr-white);
  --foreground: var(--civic-navy);
  --primary: var(--fr-blue);
  --primary-foreground: var(--fr-white);
  --secondary: var(--civic-light);
  --secondary-foreground: var(--civic-navy);
  --muted: #f1f5f9;
  --muted-foreground: var(--civic-gray);
  --accent: var(--civic-light);
  --accent-foreground: var(--civic-navy);
  --destructive: var(--fr-red);
  --destructive-foreground: var(--fr-white);
  --success: var(--civic-success);
  --success-foreground: var(--fr-white);
  --warning: var(--civic-warning);
  --warning-foreground: var(--fr-white);
  --info: var(--civic-info);
  --info-foreground: var(--fr-white);
  --border: #e2e8f0;
  --input: #e2e8f0;
  --ring: var(--fr-blue);
  --radius: 0.75rem;
}

/* Tailwind CSS integration */
@layer base {
  * {
    border-color: var(--border);
  }
  
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

/* Dark mode support with French civic theming */
@media (prefers-color-scheme: dark) {
  :root {
    --background: #0f172a;
    --foreground: #f1f5f9;
    --primary: #3b82f6;
    --primary-foreground: #f1f5f9;
    --secondary: #1e293b;
    --secondary-foreground: #f1f5f9;
    --muted: #1e293b;
    --muted-foreground: #94a3b8;
    --accent: #1e293b;
    --accent-foreground: #f1f5f9;
    --destructive: #ef4444;
    --destructive-foreground: #f1f5f9;
    --success: #10b981;
    --warning: #f59e0b;
    --info: #06b6d4;
    --border: #334155;
    --input: #334155;
    --ring: #3b82f6;
  }
}

/* French Typography System */
body {
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  line-height: 1.6;
  letter-spacing: -0.01em;
}

.font-marianne {
  font-family: "Marianne", "Inter", system-ui, sans-serif;
}

/* French Civic Component Styles */
.civic-gradient {
  background: linear-gradient(135deg, var(--fr-blue) 0%, var(--civic-info) 50%, var(--civic-success) 100%);
}

.civic-card {
  @apply bg-background border rounded-xl shadow-sm transition-all duration-200;
  border-color: var(--border);
}

.civic-card:hover {
  @apply shadow-lg border-primary/20;
}

.civic-badge {
  @apply inline-flex items-center px-3 py-1 rounded-full text-sm font-medium;
}

.civic-badge-primary {
  @apply bg-primary/10 text-primary border border-primary/20;
}

.civic-badge-success {
  background-color: rgb(var(--civic-success) / 0.1);
  color: var(--civic-success);
  border: 1px solid rgb(var(--civic-success) / 0.2);
}

.civic-badge-warning {
  background-color: rgb(var(--civic-warning) / 0.1);
  color: var(--civic-warning);
  border: 1px solid rgb(var(--civic-warning) / 0.2);
}

.civic-badge-info {
  background-color: rgb(var(--civic-info) / 0.1);
  color: var(--civic-info);
  border: 1px solid rgb(var(--civic-info) / 0.2);
}

/* French Pillar Colors */
.pillar-sante { background-color: #e74c3c; }
.pillar-education { background-color: #3498db; }
.pillar-transport { background-color: #9b59b6; }
.pillar-logement { background-color: #1abc9c; }
.pillar-emploi { background-color: #f39c12; }
.pillar-securite { background-color: #e67e22; }
.pillar-environnement { background-color: #27ae60; }
.pillar-justice { background-color: #34495e; }
.pillar-pouvoir-achat { background-color: #95a5a6; }
.pillar-vie-sociale { background-color: #ff6b6b; }
.pillar-democratie { background-color: #4ecdc4; }
.pillar-culture { background-color: #ffe66d; }

/* Accessibility Enhancements */
.focus-visible {
  @apply outline-none ring-2 ring-primary ring-offset-2 ring-offset-background;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* French UI Animations */
@keyframes civicFadeIn {
  from { 
    opacity: 0; 
    transform: translateY(8px);
  }
  to { 
    opacity: 1; 
    transform: translateY(0);
  }
}

@keyframes civicSlideIn {
  from {
    opacity: 0;
    transform: translateX(-16px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes civicScaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-civic-fade-in {
  animation: civicFadeIn 0.4s ease-out;
}

.animate-civic-slide-in {
  animation: civicSlideIn 0.3s ease-out;
}

.animate-civic-scale-in {
  animation: civicScaleIn 0.2s ease-out;
}

/* Responsive French Layout */
.civic-container {
  @apply mx-auto px-4 sm:px-6 lg:px-8;
  max-width: 1200px;
}

.civic-section {
  @apply py-12 sm:py-16 lg:py-20;
}

/* Interactive States */
.civic-interactive {
  @apply transition-all duration-200 ease-out;
}

.civic-interactive:hover {
  @apply transform scale-[1.02] shadow-lg;
}

.civic-interactive:active {
  @apply transform scale-[0.98];
}

/* French Government Accessibility Standards */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
