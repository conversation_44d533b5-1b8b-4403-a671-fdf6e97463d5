import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON>ei<PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { Suspense } from "react";

const geistSans = Geist({
    variable: "--font-geist-sans",
    subsets: ["latin"],
});

const geistMono = Geist_Mono({
    variable: "--font-geist-mono",
    subsets: ["latin"],
});

export const metadata: Metadata = {
    title: "PillarScan - La voix citoyenne qui transforme la France",
    description:
        "Plateforme citoyenne pour exprimer et suivre vos préoccupations sur les 12 piliers de la société française.",
    keywords: "citoyen, expression, France, démocratie, participation",
    authors: [{ name: "PillarScan Team" }],
    openGraph: {
        title: "PillarScan - La voix citoyenne",
        description: "Transformez vos préoccupations en actions concrètes",
        type: "website",
        locale: "fr_FR",
    },
};

export default function RootLayout({
    children,
}: Readonly<{
    children: React.ReactNode;
}>) {
    return (
        <html lang="fr">
            <body className={`${geistSans.variable} ${geistMono.variable} antialiased`}>
                <Suspense
                    fallback={
                        <div
                            className="flex items-center justify-center min-h-screen text-gray-500"
                            style={{ fontFamily: "var(--font-geist-sans)" }}
                        >
                            Loading...
                        </div>
                    }
                >
                    {children}
                </Suspense>
            </body>
        </html>
    );
}
