"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle, CardDescription } from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import Badge from "@/components/ui/Badge";
import { useAuthStore } from "@/stores/authStore";
import { useRouter } from "next/navigation";
import {
    CheckCircleIcon,
    XMarkIcon,
    EyeIcon,
    ClockIcon,
    ExclamationTriangleIcon,
    ChatBubbleBottomCenterTextIcon,
    MapPinIcon,
    CalendarIcon,
    UserIcon,
    FunnelIcon,
    AdjustmentsHorizontalIcon,
} from "@heroicons/react/24/outline";

interface ExpressionToModerate {
    id: string;
    titre: string;
    contenu: string;
    type_expression: string;
    urgence: number;
    etat_emotionnel: string;
    statut: string;
    date_creation: string;
    auteur?: {
        nom?: string;
        username?: string;
    };
    piliers?: Array<{
        id: string;
        nom: string;
        couleur: string;
    }>;
    lieu?: {
        nom: string;
    };
    analyse_ia?: {
        score_confiance: number;
        suggestions: string[];
    };
}

const mockExpressions: ExpressionToModerate[] = [
    {
        id: "1",
        titre: "Problème de propreté récurrent rue de la Paix",
        contenu:
            "Les poubelles débordent depuis une semaine rue de la Paix, entre le 45 et le 67. L'odeur devient insupportable et attire les rats. Photo jointe. Plusieurs voisins se plaignent.",
        type_expression: "probleme",
        urgence: 4,
        etat_emotionnel: "colere",
        statut: "en_moderation",
        date_creation: "2024-12-20T10:30:00Z",
        auteur: { nom: "Marie Dupont" },
        piliers: [{ id: "1", nom: "Environnement", couleur: "#27ae60" }],
        lieu: { nom: "75002 Paris" },
        analyse_ia: {
            score_confiance: 92,
            suggestions: ["Pilier: Environnement", "Urgence: Élevée", "Service: Propreté Paris"],
        },
    },
    {
        id: "2",
        titre: "Excellente nouvelle piste cyclable Boulevard Voltaire",
        contenu:
            "Je tiens à féliciter la mairie pour la nouvelle piste cyclable sécurisée sur le Boulevard Voltaire. C'est exactement ce dont nous avions besoin ! Circulation fluide et sécurisée.",
        type_expression: "satisfaction",
        urgence: 1,
        etat_emotionnel: "joie",
        statut: "en_moderation",
        date_creation: "2024-12-20T09:15:00Z",
        auteur: { username: "cycliste_parisien" },
        piliers: [{ id: "2", nom: "Transport", couleur: "#9b59b6" }],
        lieu: { nom: "75011 Paris" },
        analyse_ia: {
            score_confiance: 88,
            suggestions: ["Pilier: Transport", "Type: Satisfaction", "Partage recommandé"],
        },
    },
    {
        id: "3",
        titre: "Proposition d'amélioration pour les horaires de bus",
        contenu:
            "Serait-il possible d'étendre les horaires de la ligne de bus 67 jusqu'à minuit le week-end ? Cela faciliterait les sorties culturelles et réduirait l'usage de la voiture.",
        type_expression: "idee",
        urgence: 2,
        etat_emotionnel: "espoir",
        statut: "en_moderation",
        date_creation: "2024-12-20T08:45:00Z",
        auteur: { nom: "Jean Martin" },
        piliers: [{ id: "2", nom: "Transport", couleur: "#9b59b6" }],
        lieu: { nom: "75018 Paris" },
        analyse_ia: {
            score_confiance: 85,
            suggestions: ["Pilier: Transport", "Type: Idée constructive", "Transmission RATP"],
        },
    },
];

const getStatusColor = (status: string) => {
    switch (status) {
        case "en_moderation":
            return "warning";
        case "approuve":
            return "success";
        case "rejete":
            return "destructive";
        default:
            return "secondary";
    }
};

const getUrgencyColor = (urgence: number) => {
    if (urgence >= 4) return "destructive";
    if (urgence >= 3) return "warning";
    return "info";
};

const getTypeIcon = (type: string) => {
    switch (type) {
        case "probleme":
            return <ExclamationTriangleIcon className="h-4 w-4" />;
        case "satisfaction":
            return <CheckCircleIcon className="h-4 w-4" />;
        case "idee":
            return <ChatBubbleBottomCenterTextIcon className="h-4 w-4" />;
        default:
            return <ChatBubbleBottomCenterTextIcon className="h-4 w-4" />;
    }
};

export default function ModerationPage() {
    const { isAuthenticated, user, profile } = useAuthStore();
    const router = useRouter();
    const [expressions, setExpressions] = useState<ExpressionToModerate[]>(mockExpressions);
    const [selectedExpression, setSelectedExpression] = useState<ExpressionToModerate | null>(null);
    const [filter, setFilter] = useState<"all" | "urgent" | "low_confidence">("all");

    useEffect(() => {
        if (!isAuthenticated) {
            router.push("/auth/login");
            return;
        }

        // Check if user has moderation permissions
        if (profile?.role !== "validateur" && profile?.role !== "super_admin") {
            router.push("/dashboard");
            return;
        }
    }, [isAuthenticated, profile, router]);

    const handleApprove = async (expressionId: string) => {
        setExpressions((prev) =>
            prev.map((expr) => (expr.id === expressionId ? { ...expr, statut: "approuve" } : expr)),
        );
        setSelectedExpression(null);
    };

    const handleReject = async (expressionId: string) => {
        setExpressions((prev) =>
            prev.map((expr) => (expr.id === expressionId ? { ...expr, statut: "rejete" } : expr)),
        );
        setSelectedExpression(null);
    };

    const filteredExpressions = expressions.filter((expr) => {
        if (filter === "urgent") return expr.urgence >= 4;
        if (filter === "low_confidence") return (expr.analyse_ia?.score_confiance || 0) < 90;
        return expr.statut === "en_moderation";
    });

    if (!isAuthenticated || (profile?.role !== "validateur" && profile?.role !== "super_admin")) {
        return null; // Loading state or redirect handled in useEffect
    }

    return (
        <div className="min-h-screen bg-secondary/30">
            {/* Header */}
            <section className="bg-background border-b">
                <div className="civic-container py-6">
                    <div className="flex items-center justify-between">
                        <div>
                            <h1 className="text-3xl font-bold text-foreground">
                                Interface de modération
                            </h1>
                            <p className="text-muted-foreground mt-2">
                                Vérifiez et validez les expressions citoyennes
                            </p>
                        </div>
                        <div className="flex items-center gap-4">
                            <Badge variant="info">{filteredExpressions.length} en attente</Badge>
                            <Badge variant="primary">Validateur</Badge>
                        </div>
                    </div>
                </div>
            </section>

            <div className="civic-container py-8">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    {/* Expressions List */}
                    <div>
                        {/* Filters */}
                        <Card className="mb-6 p-4">
                            <div className="flex items-center justify-between">
                                <h3 className="font-semibold text-foreground">Filtres</h3>
                                <AdjustmentsHorizontalIcon className="h-5 w-5 text-muted-foreground" />
                            </div>
                            <div className="flex gap-2 mt-4">
                                <Button
                                    variant={filter === "all" ? "primary" : "ghost"}
                                    size="sm"
                                    onClick={() => setFilter("all")}
                                >
                                    Toutes
                                </Button>
                                <Button
                                    variant={filter === "urgent" ? "primary" : "ghost"}
                                    size="sm"
                                    onClick={() => setFilter("urgent")}
                                >
                                    Urgentes
                                </Button>
                                <Button
                                    variant={filter === "low_confidence" ? "primary" : "ghost"}
                                    size="sm"
                                    onClick={() => setFilter("low_confidence")}
                                >
                                    IA incertaine
                                </Button>
                            </div>
                        </Card>

                        {/* Expressions */}
                        <div className="space-y-4">
                            {filteredExpressions.map((expression) => (
                                <Card
                                    key={expression.id}
                                    hover
                                    interactive
                                    className={`cursor-pointer transition-all ${
                                        selectedExpression?.id === expression.id
                                            ? "ring-2 ring-primary"
                                            : ""
                                    }`}
                                    onClick={() => setSelectedExpression(expression)}
                                >
                                    <CardHeader className="pb-3">
                                        <div className="flex items-start justify-between">
                                            <div className="flex items-center gap-2">
                                                {getTypeIcon(expression.type_expression)}
                                                <Badge
                                                    variant={getUrgencyColor(expression.urgence)}
                                                    size="sm"
                                                >
                                                    Urgence {expression.urgence}
                                                </Badge>
                                                {expression.analyse_ia &&
                                                    expression.analyse_ia.score_confiance < 90 && (
                                                        <Badge variant="warning" size="sm">
                                                            IA:{" "}
                                                            {expression.analyse_ia.score_confiance}%
                                                        </Badge>
                                                    )}
                                            </div>
                                            <Badge
                                                variant={getStatusColor(expression.statut)}
                                                size="sm"
                                            >
                                                En modération
                                            </Badge>
                                        </div>
                                        <CardTitle className="text-lg leading-tight">
                                            {expression.titre}
                                        </CardTitle>
                                    </CardHeader>

                                    <CardContent>
                                        <p className="text-muted-foreground text-sm mb-4 line-clamp-2">
                                            {expression.contenu}
                                        </p>

                                        <div className="flex items-center gap-4 text-xs text-muted-foreground">
                                            <div className="flex items-center gap-1">
                                                <UserIcon className="h-3 w-3" />
                                                {expression.auteur?.nom ||
                                                    expression.auteur?.username}
                                            </div>
                                            <div className="flex items-center gap-1">
                                                <MapPinIcon className="h-3 w-3" />
                                                {expression.lieu?.nom}
                                            </div>
                                            <div className="flex items-center gap-1">
                                                <ClockIcon className="h-3 w-3" />
                                                {new Date(
                                                    expression.date_creation,
                                                ).toLocaleDateString("fr-FR")}
                                            </div>
                                        </div>

                                        {expression.piliers && expression.piliers.length > 0 && (
                                            <div className="flex gap-1 mt-3">
                                                {expression.piliers.map((pilier) => (
                                                    <Badge
                                                        key={pilier.id}
                                                        variant="outline"
                                                        size="sm"
                                                        style={{
                                                            color: pilier.couleur,
                                                            borderColor: pilier.couleur,
                                                        }}
                                                    >
                                                        {pilier.nom}
                                                    </Badge>
                                                ))}
                                            </div>
                                        )}
                                    </CardContent>
                                </Card>
                            ))}

                            {filteredExpressions.length === 0 && (
                                <Card className="p-8 text-center">
                                    <CheckCircleIcon className="h-12 w-12 text-success mx-auto mb-4" />
                                    <h3 className="text-lg font-semibold mb-2">
                                        Aucune expression en attente
                                    </h3>
                                    <p className="text-muted-foreground">
                                        Toutes les expressions ont été traitées.
                                    </p>
                                </Card>
                            )}
                        </div>
                    </div>

                    {/* Detail Panel */}
                    <div>
                        {selectedExpression ? (
                            <Card className="sticky top-4">
                                <CardHeader>
                                    <div className="flex items-center justify-between mb-4">
                                        <CardTitle>Détails de l'expression</CardTitle>
                                        <Button
                                            variant="ghost"
                                            size="sm"
                                            onClick={() => setSelectedExpression(null)}
                                        >
                                            <XMarkIcon className="h-4 w-4" />
                                        </Button>
                                    </div>

                                    <div className="space-y-4">
                                        <h3 className="text-xl font-bold text-foreground">
                                            {selectedExpression.titre}
                                        </h3>

                                        <div className="flex flex-wrap gap-2">
                                            <Badge
                                                variant={getUrgencyColor(
                                                    selectedExpression.urgence,
                                                )}
                                            >
                                                Urgence {selectedExpression.urgence}/5
                                            </Badge>
                                            <Badge variant="outline">
                                                {selectedExpression.type_expression}
                                            </Badge>
                                            <Badge variant="secondary">
                                                {selectedExpression.etat_emotionnel}
                                            </Badge>
                                        </div>
                                    </div>
                                </CardHeader>

                                <CardContent className="space-y-6">
                                    {/* Content */}
                                    <div>
                                        <h4 className="font-semibold mb-2">Contenu</h4>
                                        <p className="text-foreground leading-relaxed">
                                            {selectedExpression.contenu}
                                        </p>
                                    </div>

                                    {/* AI Analysis */}
                                    {selectedExpression.analyse_ia && (
                                        <div>
                                            <h4 className="font-semibold mb-2">Analyse IA</h4>
                                            <div className="bg-secondary/50 rounded-lg p-4">
                                                <div className="flex items-center justify-between mb-3">
                                                    <span className="text-sm text-muted-foreground">
                                                        Confiance
                                                    </span>
                                                    <Badge
                                                        variant={
                                                            selectedExpression.analyse_ia
                                                                .score_confiance >= 90
                                                                ? "success"
                                                                : "warning"
                                                        }
                                                    >
                                                        {
                                                            selectedExpression.analyse_ia
                                                                .score_confiance
                                                        }
                                                        %
                                                    </Badge>
                                                </div>
                                                <div className="space-y-1">
                                                    {selectedExpression.analyse_ia.suggestions.map(
                                                        (suggestion, index) => (
                                                            <div
                                                                key={index}
                                                                className="text-sm text-foreground"
                                                            >
                                                                • {suggestion}
                                                            </div>
                                                        ),
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                    )}

                                    {/* Author Info */}
                                    <div>
                                        <h4 className="font-semibold mb-2">Informations</h4>
                                        <div className="space-y-2 text-sm">
                                            <div className="flex justify-between">
                                                <span className="text-muted-foreground">
                                                    Auteur
                                                </span>
                                                <span>
                                                    {selectedExpression.auteur?.nom ||
                                                        selectedExpression.auteur?.username}
                                                </span>
                                            </div>
                                            <div className="flex justify-between">
                                                <span className="text-muted-foreground">
                                                    Localisation
                                                </span>
                                                <span>{selectedExpression.lieu?.nom}</span>
                                            </div>
                                            <div className="flex justify-between">
                                                <span className="text-muted-foreground">Date</span>
                                                <span>
                                                    {new Date(
                                                        selectedExpression.date_creation,
                                                    ).toLocaleDateString("fr-FR")}
                                                </span>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Actions */}
                                    <div className="flex gap-3 pt-4 border-t">
                                        <Button
                                            variant="success"
                                            className="flex-1"
                                            onClick={() => handleApprove(selectedExpression.id)}
                                            icon={<CheckCircleIcon className="h-4 w-4" />}
                                        >
                                            Approuver
                                        </Button>
                                        <Button
                                            variant="destructive"
                                            className="flex-1"
                                            onClick={() => handleReject(selectedExpression.id)}
                                            icon={<XMarkIcon className="h-4 w-4" />}
                                        >
                                            Rejeter
                                        </Button>
                                    </div>
                                </CardContent>
                            </Card>
                        ) : (
                            <Card className="p-8 text-center">
                                <EyeIcon className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                                <h3 className="text-lg font-semibold mb-2">
                                    Sélectionnez une expression
                                </h3>
                                <p className="text-muted-foreground">
                                    Cliquez sur une expression à gauche pour voir les détails et la
                                    modérer.
                                </p>
                            </Card>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
}
