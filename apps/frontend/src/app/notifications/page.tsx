"use client";

import React, { useState, useEffect } from "react";
import Link from "next/link";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import Badge from "@/components/ui/Badge";
import { useAuthStore } from "@/stores/authStore";
import { useNotificationStore, Notification } from "@/stores/notificationStore";
import { useRouter } from "next/navigation";
import {
    BellIcon,
    CheckIcon,
    XMarkIcon,
    TrashIcon,
    FunnelIcon,
    CheckCircleIcon,
    ExclamationTriangleIcon,
    InformationCircleIcon,
    ClockIcon,
    EyeIcon,
    ArrowLeftIcon,
} from "@heroicons/react/24/outline";

const getNotificationIcon = (type: Notification["type"]) => {
    switch (type) {
        case "success":
            return <CheckCircleIcon className="h-5 w-5 text-success" />;
        case "warning":
            return <ExclamationTriangleIcon className="h-5 w-5 text-warning" />;
        case "error":
            return <XMarkIcon className="h-5 w-5 text-destructive" />;
        default:
            return <InformationCircleIcon className="h-5 w-5 text-info" />;
    }
};

const getNotificationBg = (type: Notification["type"]) => {
    switch (type) {
        case "success":
            return "border-l-success bg-success/5";
        case "warning":
            return "border-l-warning bg-warning/5";
        case "error":
            return "border-l-destructive bg-destructive/5";
        default:
            return "border-l-info bg-info/5";
    }
};

const formatTime = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return "À l'instant";
    if (minutes < 60) return `Il y a ${minutes} min`;
    if (hours < 24) return `Il y a ${hours}h`;
    if (days < 7) return `Il y a ${days}j`;
    return date.toLocaleDateString("fr-FR", {
        day: "numeric",
        month: "long",
        year: "numeric",
        hour: "2-digit",
        minute: "2-digit",
    });
};

export default function NotificationsPage() {
    const [mounted, setMounted] = useState(false);
    const { isAuthenticated } = useAuthStore();
    const router = useRouter();

    useEffect(() => {
        setMounted(true);
    }, []);
    const {
        notifications,
        unreadCount,
        isConnected,
        markAsRead,
        markAllAsRead,
        removeNotification,
        clearAll,
    } = useNotificationStore();

    const [filter, setFilter] = useState<
        "all" | "unread" | "info" | "success" | "warning" | "error"
    >("all");

    if (!mounted) {
        return null;
    }

    if (!isAuthenticated) {
        router.push("/auth/login");
        return null;
    }

    const filteredNotifications = notifications.filter((notification) => {
        if (filter === "unread") return !notification.read;
        if (filter === "all") return true;
        return notification.type === filter;
    });

    const handleNotificationClick = (notification: Notification) => {
        if (!notification.read) {
            markAsRead(notification.id);
        }
        if (notification.actionUrl && typeof window !== "undefined") {
            router.push(notification.actionUrl);
        }
    };

    return (
        <div className="min-h-screen bg-secondary/30">
            {/* Header */}
            <section className="bg-background border-b">
                <div className="civic-container py-6">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                            <Button
                                variant="ghost"
                                onClick={() => router.back()}
                                icon={<ArrowLeftIcon className="h-4 w-4" />}
                            >
                                Retour
                            </Button>
                            <div>
                                <h1 className="text-3xl font-bold text-foreground">
                                    Notifications
                                </h1>
                                <p className="text-muted-foreground mt-1">
                                    Gérez vos notifications et restez informé de l'activité
                                </p>
                            </div>
                        </div>
                        <div className="flex items-center gap-4">
                            <Badge variant={isConnected ? "success" : "warning"} dot>
                                {isConnected ? "Connecté" : "Déconnecté"}
                            </Badge>
                            <Badge variant="info">
                                {unreadCount} non lue{unreadCount !== 1 ? "s" : ""}
                            </Badge>
                        </div>
                    </div>
                </div>
            </section>

            <div className="civic-container py-8">
                <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
                    {/* Filters Sidebar */}
                    <div className="lg:col-span-1">
                        <Card className="p-6 sticky top-4">
                            <CardHeader className="px-0 pt-0">
                                <CardTitle className="flex items-center gap-2">
                                    <FunnelIcon className="h-5 w-5" />
                                    Filtres
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="px-0">
                                <div className="space-y-2">
                                    {[
                                        {
                                            key: "all",
                                            label: "Toutes",
                                            count: notifications.length,
                                        },
                                        { key: "unread", label: "Non lues", count: unreadCount },
                                        {
                                            key: "info",
                                            label: "Informations",
                                            count: notifications.filter((n) => n.type === "info")
                                                .length,
                                        },
                                        {
                                            key: "success",
                                            label: "Succès",
                                            count: notifications.filter((n) => n.type === "success")
                                                .length,
                                        },
                                        {
                                            key: "warning",
                                            label: "Avertissements",
                                            count: notifications.filter((n) => n.type === "warning")
                                                .length,
                                        },
                                        {
                                            key: "error",
                                            label: "Erreurs",
                                            count: notifications.filter((n) => n.type === "error")
                                                .length,
                                        },
                                    ].map((filterOption) => (
                                        <Button
                                            key={filterOption.key}
                                            variant={
                                                filter === filterOption.key ? "primary" : "ghost"
                                            }
                                            size="sm"
                                            className="w-full justify-between"
                                            onClick={() => setFilter(filterOption.key as any)}
                                        >
                                            <span>{filterOption.label}</span>
                                            <Badge variant="outline" size="sm">
                                                {filterOption.count}
                                            </Badge>
                                        </Button>
                                    ))}
                                </div>

                                <div className="mt-6 pt-6 border-t border-border space-y-3">
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        className="w-full"
                                        onClick={markAllAsRead}
                                        disabled={unreadCount === 0}
                                        icon={<CheckIcon className="h-4 w-4" />}
                                    >
                                        Tout marquer lu
                                    </Button>
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        className="w-full"
                                        onClick={clearAll}
                                        disabled={notifications.length === 0}
                                        icon={<TrashIcon className="h-4 w-4" />}
                                    >
                                        Tout effacer
                                    </Button>
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Notifications List */}
                    <div className="lg:col-span-3">
                        {filteredNotifications.length === 0 ? (
                            <Card className="p-12 text-center">
                                <BellIcon className="h-16 w-16 text-muted-foreground mx-auto mb-6" />
                                <h3 className="text-xl font-semibold mb-4">
                                    {filter === "unread"
                                        ? "Aucune notification non lue"
                                        : "Aucune notification"}
                                </h3>
                                <p className="text-muted-foreground mb-6">
                                    {filter === "unread"
                                        ? "Toutes vos notifications ont été lues."
                                        : "Vous n'avez pas encore de notifications."}
                                </p>
                                <Button variant="primary">
                                    <Link href="/dashboard">Retour au tableau de bord</Link>
                                </Button>
                            </Card>
                        ) : (
                            <div className="space-y-4">
                                {filteredNotifications.map((notification) => (
                                    <Card
                                        key={notification.id}
                                        className={`cursor-pointer transition-all duration-200 hover:shadow-md border-l-4 ${getNotificationBg(
                                            notification.type,
                                        )} ${!notification.read ? "ring-1 ring-primary/20" : ""}`}
                                        onClick={() => handleNotificationClick(notification)}
                                    >
                                        <CardContent className="p-6">
                                            <div className="flex items-start gap-4">
                                                <div className="flex-shrink-0 mt-1">
                                                    {getNotificationIcon(notification.type)}
                                                </div>

                                                <div className="flex-1 min-w-0">
                                                    <div className="flex items-start justify-between mb-2">
                                                        <h4 className="text-lg font-semibold text-foreground truncate pr-4">
                                                            {notification.title}
                                                        </h4>
                                                        <div className="flex items-center gap-2 flex-shrink-0">
                                                            {!notification.read && (
                                                                <div className="w-3 h-3 bg-primary rounded-full" />
                                                            )}
                                                            <Button
                                                                variant="ghost"
                                                                size="xs"
                                                                onClick={(e) => {
                                                                    e.stopPropagation();
                                                                    removeNotification(
                                                                        notification.id,
                                                                    );
                                                                }}
                                                                className="opacity-0 group-hover:opacity-100 transition-opacity"
                                                            >
                                                                <XMarkIcon className="h-4 w-4" />
                                                            </Button>
                                                        </div>
                                                    </div>

                                                    <p className="text-foreground mb-4 leading-relaxed">
                                                        {notification.message}
                                                    </p>

                                                    <div className="flex items-center justify-between">
                                                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                                            <ClockIcon className="h-4 w-4" />
                                                            {formatTime(notification.timestamp)}
                                                        </div>

                                                        <div className="flex items-center gap-3">
                                                            {notification.actionUrl &&
                                                                notification.actionLabel && (
                                                                    <Button
                                                                        variant="outline"
                                                                        size="sm"
                                                                        onClick={(e) => {
                                                                            e.stopPropagation();
                                                                            handleNotificationClick(
                                                                                notification,
                                                                            );
                                                                        }}
                                                                    >
                                                                        {notification.actionLabel}
                                                                    </Button>
                                                                )}

                                                            {!notification.read && (
                                                                <Button
                                                                    variant="ghost"
                                                                    size="sm"
                                                                    onClick={(e) => {
                                                                        e.stopPropagation();
                                                                        markAsRead(notification.id);
                                                                    }}
                                                                    icon={
                                                                        <EyeIcon className="h-4 w-4" />
                                                                    }
                                                                >
                                                                    Marquer lu
                                                                </Button>
                                                            )}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </CardContent>
                                    </Card>
                                ))}
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
}
