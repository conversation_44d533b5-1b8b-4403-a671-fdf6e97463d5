"use client";

import React, { useState, useEffect } from "react";
import Link from "next/link";
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import Badge from "@/components/ui/Badge";
import { piliersApi } from "@/lib/api";
import {
    ChartBarSquareIcon,
    MapPinIcon,
    UsersIcon,
    ChartBarIcon,
} from "@heroicons/react/24/outline";

interface Pilier {
    id: string;
    code: string;
    nom: string;
    description: string;
    couleur: string;
    icone: string;
    sous_piliers?: any[];
}

const pillarIcons: Record<string, string> = {
    SANTE: "🏥",
    EDUCATION: "🎓",
    TRANSPORT: "🚇",
    LOGEMENT: "🏘️",
    EMPLOI: "💼",
    SECURITE: "🛡️",
    ENVIRONNEMENT: "🌳",
    JUSTICE: "⚖️",
    POUVOIR_ACHAT: "💰",
    VIE_SOCIALE: "🤝",
    DEMOCRATIE: "🏛️",
    CULTURE: "🎭",
};

export default function PiliersPage() {
    const [piliers, setPiliers] = useState<Pilier[]>([]);
    const [loading, setLoading] = useState(true);
    const [selectedPilier, setSelectedPilier] = useState<string | null>(null);

    useEffect(() => {
        loadPiliers();
    }, []);

    const loadPiliers = async () => {
        try {
            const response = await piliersApi.getAll();
            setPiliers((response.data as any) || []);
        } catch (error) {
            console.error("Erreur lors du chargement des piliers:", error);
        } finally {
            setLoading(false);
        }
    };

    const mockStats = {
        totalExpressions: 12547,
        activeExpressions: 3421,
        resolvedThisMonth: 1834,
        participatingCities: 2847,
    };

    if (loading) {
        return (
            <div className="min-h-screen bg-secondary/30 flex items-center justify-center">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
                    <p className="text-muted-foreground">Chargement des piliers...</p>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-secondary/30">
            {/* Header */}
            <section className="civic-gradient text-white py-16">
                <div className="civic-container">
                    <div className="text-center max-w-4xl mx-auto">
                        <Badge
                            variant="primary"
                            size="lg"
                            className="mb-6 bg-white/20 text-white border-white/30"
                        >
                            Les 12 Piliers de la Société Française
                        </Badge>
                        <h1 className="text-4xl md:text-6xl font-bold mb-6 font-marianne">
                            Tous les domaines de votre vie citoyenne
                        </h1>
                        <p className="text-xl md:text-2xl mb-8 leading-relaxed opacity-90">
                            Explorez les 12 piliers qui structurent notre société et découvrez
                            comment votre voix peut transformer chaque aspect de votre quotidien.
                        </p>

                        {/* Quick Stats */}
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-3xl mx-auto">
                            <div className="text-center">
                                <div className="text-3xl font-bold">
                                    {mockStats.totalExpressions.toLocaleString()}
                                </div>
                                <div className="text-sm opacity-80">Expressions totales</div>
                            </div>
                            <div className="text-center">
                                <div className="text-3xl font-bold">
                                    {mockStats.activeExpressions.toLocaleString()}
                                </div>
                                <div className="text-sm opacity-80">En traitement</div>
                            </div>
                            <div className="text-center">
                                <div className="text-3xl font-bold">
                                    {mockStats.resolvedThisMonth.toLocaleString()}
                                </div>
                                <div className="text-sm opacity-80">Résolues ce mois</div>
                            </div>
                            <div className="text-center">
                                <div className="text-3xl font-bold">
                                    {mockStats.participatingCities.toLocaleString()}
                                </div>
                                <div className="text-sm opacity-80">Communes participantes</div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* Piliers Grid */}
            <section className="civic-section">
                <div className="civic-container">
                    {piliers.length === 0 ? (
                        <div className="text-center py-16">
                            <p className="text-xl text-muted-foreground mb-8">
                                Aucun pilier trouvé. Les données seront disponibles une fois le
                                backend démarré.
                            </p>
                            <Button variant="primary" size="lg">
                                <Link href="/expressions/new">Créer une expression</Link>
                            </Button>
                        </div>
                    ) : (
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                            {piliers.map((pilier, index) => (
                                <Card
                                    key={pilier.id}
                                    hover
                                    interactive
                                    className="group cursor-pointer animate-civic-fade-in"
                                    style={{ animationDelay: `${index * 100}ms` }}
                                    onClick={() =>
                                        setSelectedPilier(
                                            selectedPilier === pilier.id ? null : pilier.id,
                                        )
                                    }
                                >
                                    <CardHeader>
                                        <div className="flex items-center mb-4">
                                            <div
                                                className="w-12 h-12 rounded-xl flex items-center justify-center text-2xl mr-4 group-hover:scale-110 transition-transform"
                                                style={{ backgroundColor: pilier.couleur }}
                                            >
                                                {pillarIcons[pilier.code] || "📊"}
                                            </div>
                                            <div>
                                                <CardTitle className="text-lg group-hover:text-primary transition-colors">
                                                    {pilier.nom}
                                                </CardTitle>
                                                <Badge variant="outline" size="sm" className="mt-1">
                                                    {pilier.code}
                                                </Badge>
                                            </div>
                                        </div>
                                    </CardHeader>

                                    <CardContent>
                                        <CardDescription className="mb-4 leading-relaxed">
                                            {pilier.description ||
                                                `Explorez toutes les expressions citoyennes liées au domaine ${pilier.nom.toLowerCase()}.`}
                                        </CardDescription>

                                        {/* Mock Statistics */}
                                        <div className="grid grid-cols-2 gap-4 mb-4">
                                            <div className="text-center p-3 bg-secondary/50 rounded-lg">
                                                <div className="text-lg font-bold text-primary">
                                                    {Math.floor(Math.random() * 500) + 100}
                                                </div>
                                                <div className="text-xs text-muted-foreground">
                                                    Expressions
                                                </div>
                                            </div>
                                            <div className="text-center p-3 bg-secondary/50 rounded-lg">
                                                <div className="text-lg font-bold text-success">
                                                    {Math.floor(Math.random() * 80) + 60}%
                                                </div>
                                                <div className="text-xs text-muted-foreground">
                                                    Résolues
                                                </div>
                                            </div>
                                        </div>

                                        <div className="flex gap-2">
                                            <Button
                                                variant="primary"
                                                size="sm"
                                                className="flex-1"
                                                icon={<ChartBarSquareIcon className="h-4 w-4" />}
                                            >
                                                Explorer
                                            </Button>
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                className="flex-1"
                                                icon={<MapPinIcon className="h-4 w-4" />}
                                            >
                                                <Link href={`/expressions/new?pilier=${pilier.id}`}>
                                                    Exprimer
                                                </Link>
                                            </Button>
                                        </div>

                                        {/* Expanded Content */}
                                        {selectedPilier === pilier.id && (
                                            <div className="mt-6 pt-6 border-t border-gray-200 animate-civic-fade-in">
                                                <h4 className="font-semibold mb-3 text-foreground">
                                                    Sous-catégories populaires :
                                                </h4>
                                                <div className="flex flex-wrap gap-2 mb-4">
                                                    {[
                                                        "Qualité",
                                                        "Accessibilité",
                                                        "Équité",
                                                        "Innovation",
                                                    ].map((tag) => (
                                                        <Badge
                                                            key={tag}
                                                            variant="secondary"
                                                            size="sm"
                                                        >
                                                            {tag}
                                                        </Badge>
                                                    ))}
                                                </div>

                                                <div className="space-y-2">
                                                    <div className="flex items-center text-sm text-muted-foreground">
                                                        <ChartBarIcon className="h-4 w-4 mr-2" />
                                                        Tendance : +
                                                        {Math.floor(Math.random() * 20) + 5}% ce
                                                        mois
                                                    </div>
                                                    <div className="flex items-center text-sm text-muted-foreground">
                                                        <UsersIcon className="h-4 w-4 mr-2" />
                                                        {Math.floor(Math.random() * 10000) +
                                                            5000}{" "}
                                                        citoyens concernés
                                                    </div>
                                                </div>
                                            </div>
                                        )}
                                    </CardContent>
                                </Card>
                            ))}
                        </div>
                    )}
                </div>
            </section>

            {/* Call to Action */}
            <section className="civic-section bg-background border-t">
                <div className="civic-container text-center">
                    <h2 className="text-3xl md:text-4xl font-bold mb-6 text-foreground">
                        Votre voix compte sur chaque pilier
                    </h2>
                    <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
                        Quel que soit le domaine qui vous préoccupe, PillarScan vous donne les
                        outils pour transformer vos frustrations en améliorations concrètes.
                    </p>

                    <div className="flex flex-col sm:flex-row gap-4 justify-center">
                        <Button size="lg" variant="primary">
                            <Link href="/expressions/new">Soumettre une expression</Link>
                        </Button>
                        <Button size="lg" variant="outline">
                            <Link href="/expressions">Voir toutes les expressions</Link>
                        </Button>
                    </div>
                </div>
            </section>
        </div>
    );
}
