"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuthStore } from "@/stores/authStore";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import Input from "@/components/ui/Input";
import Badge from "@/components/ui/Badge";
import LocationPicker from "@/components/maps/LocationPicker";
import { profilesApi } from "@/lib/api";
import { Lieu } from "@/types";
import {
    UserIcon,
    PencilIcon,
    CheckIcon,
    XMarkIcon,
    ShieldCheckIcon,
    MapPinIcon,
    CalendarIcon,
    EnvelopeIcon,
} from "@heroicons/react/24/outline";

export default function ProfilePage() {
    const router = useRouter();
    const { isAuthenticated, user, profile, loadUser } = useAuthStore();
    const [isEditing, setIsEditing] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [successMessage, setSuccessMessage] = useState<string | null>(null);

    const [formData, setFormData] = useState({
        nom: "",
        telephone: "",
        date_naissance: "",
        genre: "M" as "M" | "F" | "Autre",
        lieu_residence: null as Lieu | null,
        preferences: {
            notifications_email: true,
            notifications_push: true,
            theme: "light" as "light" | "dark" | "auto",
            langue: "fr",
        },
    });

    useEffect(() => {
        if (!isAuthenticated) {
            router.push("/auth/login");
            return;
        }

        if (profile) {
            setFormData({
                nom: profile.nom || "",
                telephone: profile.telephone || "",
                date_naissance: profile.date_naissance || "",
                genre: profile.genre || "M",
                lieu_residence: profile.lieu_residence || null,
                preferences: {
                    notifications_email: profile.preferences?.notifications_email ?? true,
                    notifications_push: profile.preferences?.notifications_push ?? true,
                    theme: (profile.preferences?.theme as "light" | "dark" | "auto") || "light",
                    langue: profile.preferences?.langue || "fr",
                },
            });
        }
    }, [isAuthenticated, profile, router]);

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
        const { name, value } = e.target;
        setFormData((prev) => ({
            ...prev,
            [name]: value,
        }));
    };

    const handlePreferenceChange = (key: string, value: any) => {
        setFormData((prev) => ({
            ...prev,
            preferences: {
                ...prev.preferences,
                [key]: value,
            },
        }));
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!profile?.id) return;

        setIsLoading(true);
        setError(null);
        setSuccessMessage(null);

        try {
            await profilesApi.updateMe(formData);
            await loadUser(); // Refresh profile data
            setIsEditing(false);
            setSuccessMessage("Profil mis à jour avec succès !");

            // Clear success message after 3 seconds
            setTimeout(() => setSuccessMessage(null), 3000);
        } catch (error: any) {
            setError(error.message || "Erreur lors de la mise à jour du profil");
        } finally {
            setIsLoading(false);
        }
    };

    const handleCancel = () => {
        if (profile) {
            setFormData({
                nom: profile.nom || "",
                telephone: profile.telephone || "",
                date_naissance: profile.date_naissance || "",
                genre: profile.genre || "M",
                lieu_residence: profile.lieu_residence || null,
                preferences: {
                    notifications_email: profile.preferences?.notifications_email ?? true,
                    notifications_push: profile.preferences?.notifications_push ?? true,
                    theme: (profile.preferences?.theme as "light" | "dark" | "auto") || "light",
                    langue: profile.preferences?.langue || "fr",
                },
            });
        }
        setIsEditing(false);
        setError(null);
    };

    const getRoleBadge = (role: string) => {
        const roleConfig = {
            observateur: { label: "Observateur", variant: "secondary" as const },
            contributeur: { label: "Contributeur", variant: "primary" as const },
            contributeur_verifie: { label: "Contributeur Vérifié", variant: "info" as const },
            validateur: { label: "Validateur", variant: "warning" as const },
            validateur_senior: { label: "Validateur Senior", variant: "warning" as const },
            admin_regional: { label: "Admin Régional", variant: "destructive" as const },
            admin_national: { label: "Admin National", variant: "destructive" as const },
            super_admin: { label: "Super Admin", variant: "destructive" as const },
        };

        const config = roleConfig[role as keyof typeof roleConfig] || {
            label: role,
            variant: "secondary" as const,
        };

        return <Badge variant={config.variant}>{config.label}</Badge>;
    };

    if (!isAuthenticated || !profile) {
        return null;
    }

    return (
        <div className="min-h-screen bg-secondary/30">
            {/* Header */}
            <section className="civic-gradient text-white py-12">
                <div className="civic-container">
                    <div className="flex items-center justify-between">
                        <div>
                            <Badge
                                variant="primary"
                                size="lg"
                                className="mb-4 bg-white/20 text-white border-white/30"
                            >
                                Mon Profil
                            </Badge>
                            <h1 className="text-4xl font-bold mb-4 font-marianne">
                                Gestion de votre compte
                            </h1>
                            <p className="text-xl opacity-90 max-w-2xl">
                                Gérez vos informations personnelles et vos préférences pour une
                                expérience optimale sur PillarScan.
                            </p>
                        </div>
                        <div className="flex items-center gap-4">
                            {!isEditing ? (
                                <Button
                                    variant="secondary"
                                    icon={<PencilIcon className="h-4 w-4" />}
                                    onClick={() => setIsEditing(true)}
                                >
                                    Modifier
                                </Button>
                            ) : (
                                <div className="flex gap-2">
                                    <Button
                                        variant="outline"
                                        className="border-white text-white hover:bg-white hover:text-primary"
                                        icon={<XMarkIcon className="h-4 w-4" />}
                                        onClick={handleCancel}
                                        disabled={isLoading}
                                    >
                                        Annuler
                                    </Button>
                                    <Button
                                        variant="secondary"
                                        icon={<CheckIcon className="h-4 w-4" />}
                                        onClick={handleSubmit}
                                        loading={isLoading}
                                    >
                                        Sauvegarder
                                    </Button>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </section>

            <div className="civic-container py-8">
                {/* Messages */}
                {error && (
                    <div className="mb-6 p-4 bg-destructive/10 border border-destructive/20 rounded-lg">
                        <p className="text-sm text-destructive">{error}</p>
                    </div>
                )}

                {successMessage && (
                    <div className="mb-6 p-4 bg-success/10 border border-success/20 rounded-lg">
                        <p className="text-sm text-success">{successMessage}</p>
                    </div>
                )}

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    {/* Profile Summary */}
                    <div className="lg:col-span-1">
                        <Card className="p-6">
                            <div className="text-center">
                                <div className="w-20 h-20 bg-primary/10 rounded-full mx-auto mb-4 flex items-center justify-center">
                                    <UserIcon className="h-10 w-10 text-primary" />
                                </div>
                                <h3 className="text-xl font-bold text-foreground mb-2">
                                    {profile.nom || user?.username || "Utilisateur"}
                                </h3>
                                <div className="mb-4">{getRoleBadge(profile.role)}</div>
                                <div className="space-y-2 text-sm text-muted-foreground">
                                    <div className="flex items-center justify-center gap-2">
                                        <EnvelopeIcon className="h-4 w-4" />
                                        <span>{user?.email}</span>
                                    </div>
                                    {profile.lieu_residence && (
                                        <div className="flex items-center justify-center gap-2">
                                            <MapPinIcon className="h-4 w-4" />
                                            <span>{profile.lieu_residence?.nom || ""}</span>
                                        </div>
                                    )}
                                    <div className="flex items-center justify-center gap-2">
                                        <CalendarIcon className="h-4 w-4" />
                                        <span>
                                            Membre depuis{" "}
                                            {new Date(
                                                profile.date_inscription ||
                                                    user?.createdAt ||
                                                    new Date(),
                                            ).toLocaleDateString("fr-FR")}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </Card>

                        {/* Statistics */}
                        <Card className="p-6 mt-6">
                            <CardHeader className="px-0 pt-0">
                                <CardTitle className="flex items-center gap-2">
                                    <ShieldCheckIcon className="h-5 w-5 text-primary" />
                                    Statistiques
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="px-0 space-y-4">
                                <div className="flex justify-between">
                                    <span className="text-sm text-muted-foreground">
                                        Expressions soumises
                                    </span>
                                    <span className="font-medium">
                                        {(profile as any)?.statistiques?.expressions_soumises || 0}
                                    </span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-sm text-muted-foreground">
                                        Score de confiance
                                    </span>
                                    <span className="font-medium">
                                        {(profile as any)?.score_confiance || 0}%
                                    </span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-sm text-muted-foreground">
                                        Validations effectuées
                                    </span>
                                    <span className="font-medium">
                                        {(profile as any)?.statistiques?.validations_effectuees ||
                                            0}
                                    </span>
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Profile Form */}
                    <div className="lg:col-span-2">
                        <form onSubmit={handleSubmit} className="space-y-6">
                            {/* Personal Information */}
                            <Card className="p-6">
                                <CardHeader className="px-0 pt-0">
                                    <CardTitle>Informations personnelles</CardTitle>
                                </CardHeader>
                                <CardContent className="px-0 space-y-4">
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label className="block text-sm font-medium text-foreground mb-1">
                                                Nom complet *
                                            </label>
                                            <Input
                                                name="nom"
                                                value={formData.nom}
                                                onChange={handleInputChange}
                                                disabled={!isEditing}
                                                required
                                            />
                                        </div>

                                        <div>
                                            <label className="block text-sm font-medium text-foreground mb-1">
                                                Téléphone
                                            </label>
                                            <Input
                                                name="telephone"
                                                type="tel"
                                                value={formData.telephone}
                                                onChange={handleInputChange}
                                                disabled={!isEditing}
                                                placeholder="+33 1 23 45 67 89"
                                            />
                                        </div>
                                    </div>

                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label className="block text-sm font-medium text-foreground mb-1">
                                                Date de naissance
                                            </label>
                                            <Input
                                                name="date_naissance"
                                                type="date"
                                                value={formData.date_naissance}
                                                onChange={handleInputChange}
                                                disabled={!isEditing}
                                            />
                                        </div>

                                        <div>
                                            <label className="block text-sm font-medium text-foreground mb-1">
                                                Genre
                                            </label>
                                            <select
                                                name="genre"
                                                value={formData.genre}
                                                onChange={handleInputChange}
                                                disabled={!isEditing}
                                                className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary disabled:opacity-50"
                                            >
                                                <option value="M">Masculin</option>
                                                <option value="F">Féminin</option>
                                                <option value="Autre">Autre</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-foreground mb-1">
                                            Lieu de résidence
                                        </label>
                                        {isEditing ? (
                                            <LocationPicker
                                                onLocationSelect={(location) => {
                                                    setFormData((prev) => ({
                                                        ...prev,
                                                        lieu_residence: location
                                                            ? ({
                                                                  id: location.id,
                                                                  nom: location.name,
                                                                  type: "adresse",
                                                                  niveau: "ville",
                                                                  coordonnees: {
                                                                      lat: location.coordinates.lat,
                                                                      lng: location.coordinates.lng,
                                                                  },
                                                                  adresse_complete:
                                                                      location.fullName,
                                                                  pays: "France",
                                                                  actif: true,
                                                                  verifie: true,
                                                              } as Lieu)
                                                            : null,
                                                    }));
                                                }}
                                                defaultValue={
                                                    formData.lieu_residence
                                                        ? {
                                                              id: formData.lieu_residence.id,
                                                              name: formData.lieu_residence.nom,
                                                              fullName:
                                                                  formData.lieu_residence
                                                                      .adresse_complete ||
                                                                  formData.lieu_residence.nom,
                                                              coordinates: {
                                                                  lat: formData.lieu_residence
                                                                      .coordonnees.lat,
                                                                  lng: formData.lieu_residence
                                                                      .coordonnees.lng,
                                                              },
                                                              type: "city" as const,
                                                          }
                                                        : undefined
                                                }
                                                placeholder="Sélectionnez votre lieu de résidence..."
                                            />
                                        ) : (
                                            <Input
                                                value={formData.lieu_residence?.nom || ""}
                                                disabled
                                                placeholder="Aucun lieu de résidence défini"
                                            />
                                        )}
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Preferences */}
                            <Card className="p-6">
                                <CardHeader className="px-0 pt-0">
                                    <CardTitle>Préférences</CardTitle>
                                </CardHeader>
                                <CardContent className="px-0 space-y-4">
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label className="block text-sm font-medium text-foreground mb-1">
                                                Thème
                                            </label>
                                            <select
                                                value={formData.preferences.theme}
                                                onChange={(e) =>
                                                    handlePreferenceChange("theme", e.target.value)
                                                }
                                                disabled={!isEditing}
                                                className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary disabled:opacity-50"
                                            >
                                                <option value="light">Clair</option>
                                                <option value="dark">Sombre</option>
                                                <option value="auto">Automatique</option>
                                            </select>
                                        </div>

                                        <div>
                                            <label className="block text-sm font-medium text-foreground mb-1">
                                                Langue
                                            </label>
                                            <select
                                                value={formData.preferences.langue}
                                                onChange={(e) =>
                                                    handlePreferenceChange("langue", e.target.value)
                                                }
                                                disabled={!isEditing}
                                                className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary disabled:opacity-50"
                                            >
                                                <option value="fr">Français</option>
                                                <option value="en">English</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div className="space-y-3">
                                        <div className="flex items-center justify-between">
                                            <label className="text-sm font-medium text-foreground">
                                                Notifications par email
                                            </label>
                                            <button
                                                type="button"
                                                onClick={() =>
                                                    handlePreferenceChange(
                                                        "notifications_email",
                                                        !formData.preferences.notifications_email,
                                                    )
                                                }
                                                disabled={!isEditing}
                                                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 disabled:opacity-50 ${
                                                    formData.preferences.notifications_email
                                                        ? "bg-primary"
                                                        : "bg-secondary"
                                                }`}
                                            >
                                                <span
                                                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                                                        formData.preferences.notifications_email
                                                            ? "translate-x-6"
                                                            : "translate-x-1"
                                                    }`}
                                                />
                                            </button>
                                        </div>

                                        <div className="flex items-center justify-between">
                                            <label className="text-sm font-medium text-foreground">
                                                Notifications push
                                            </label>
                                            <button
                                                type="button"
                                                onClick={() =>
                                                    handlePreferenceChange(
                                                        "notifications_push",
                                                        !formData.preferences.notifications_push,
                                                    )
                                                }
                                                disabled={!isEditing}
                                                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 disabled:opacity-50 ${
                                                    formData.preferences.notifications_push
                                                        ? "bg-primary"
                                                        : "bg-secondary"
                                                }`}
                                            >
                                                <span
                                                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                                                        formData.preferences.notifications_push
                                                            ? "translate-x-6"
                                                            : "translate-x-1"
                                                    }`}
                                                />
                                            </button>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    );
}
