"use client";

import React, { useState } from "react";
import { cn } from "@/utils";
import Button from "@/components/ui/Button";
import Badge from "@/components/ui/Badge";
import { Card } from "@/components/ui/Card";
import {
    XMarkIcon,
    CheckIcon,
    CalendarIcon,
    MapPinIcon,
    TagIcon,
    UserGroupIcon,
} from "@heroicons/react/24/outline";

export interface FilterOptions {
    piliers?: string[];
    typeExpression?: string[];
    urgence?: number[];
    statut?: string[];
    dateRange?: {
        start: Date;
        end: Date;
    };
    lieu?: string;
    entites?: string[];
    tags?: string[];
}

interface AdvancedFiltersProps {
    isOpen: boolean;
    onClose: () => void;
    filters: FilterOptions;
    onFiltersChange: (filters: FilterOptions) => void;
    onApply: () => void;
    onReset: () => void;
    availableOptions?: {
        piliers?: Array<{ id: string; nom: string; couleur: string }>;
        lieux?: Array<{ id: string; nom: string }>;
        entites?: Array<{ id: string; nom: string }>;
        tags?: string[];
    };
}

const AdvancedFilters: React.FC<AdvancedFiltersProps> = ({
    isOpen,
    onClose,
    filters,
    onFiltersChange,
    onApply,
    onReset,
    availableOptions = {},
}) => {
    const [localFilters, setLocalFilters] = useState<FilterOptions>(filters);

    const handleFilterChange = (key: keyof FilterOptions, value: any) => {
        setLocalFilters(prev => ({
            ...prev,
            [key]: value,
        }));
    };

    const handleApply = () => {
        onFiltersChange(localFilters);
        onApply();
    };

    const handleReset = () => {
        const emptyFilters: FilterOptions = {
            piliers: [],
            typeExpression: [],
            urgence: [],
            statut: [],
            dateRange: undefined,
            lieu: undefined,
            entites: [],
            tags: [],
        };
        setLocalFilters(emptyFilters);
        onFiltersChange(emptyFilters);
        onReset();
    };

    const getActiveFilterCount = () => {
        let count = 0;
        if (localFilters.piliers?.length) count += localFilters.piliers.length;
        if (localFilters.typeExpression?.length) count += localFilters.typeExpression.length;
        if (localFilters.urgence?.length) count += localFilters.urgence.length;
        if (localFilters.statut?.length) count += localFilters.statut.length;
        if (localFilters.dateRange) count += 1;
        if (localFilters.lieu) count += 1;
        if (localFilters.entites?.length) count += localFilters.entites.length;
        if (localFilters.tags?.length) count += localFilters.tags.length;
        return count;
    };

    const toggleArrayFilter = (
        key: keyof FilterOptions,
        value: string | number
    ) => {
        const currentArray = (localFilters[key] as any[]) || [];
        const newArray = currentArray.includes(value)
            ? currentArray.filter(v => v !== value)
            : [...currentArray, value];
        handleFilterChange(key, newArray);
    };

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 z-50 overflow-hidden">
            {/* Backdrop */}
            <div
                className="absolute inset-0 bg-black/50 transition-opacity"
                onClick={onClose}
            />

            {/* Sidebar */}
            <div className="absolute right-0 top-0 h-full w-full max-w-md bg-background shadow-xl">
                <div className="flex h-full flex-col">
                    {/* Header */}
                    <div className="flex items-center justify-between border-b p-6">
                        <div>
                            <h2 className="text-xl font-bold text-foreground">
                                Filtres avancés
                            </h2>
                            {getActiveFilterCount() > 0 && (
                                <p className="text-sm text-muted-foreground mt-1">
                                    {getActiveFilterCount()} filtre{getActiveFilterCount() > 1 ? "s" : ""} actif{getActiveFilterCount() > 1 ? "s" : ""}
                                </p>
                            )}
                        </div>
                        <button
                            onClick={onClose}
                            className="rounded-lg p-2 hover:bg-secondary transition-colors"
                        >
                            <XMarkIcon className="h-5 w-5" />
                        </button>
                    </div>

                    {/* Filters Content */}
                    <div className="flex-1 overflow-y-auto p-6 space-y-6">
                        {/* Piliers */}
                        {availableOptions.piliers && (
                            <div>
                                <h3 className="text-sm font-medium text-foreground mb-3">
                                    Piliers de société
                                </h3>
                                <div className="grid grid-cols-2 gap-2">
                                    {availableOptions.piliers.map(pilier => (
                                        <button
                                            key={pilier.id}
                                            onClick={() => toggleArrayFilter("piliers", pilier.id)}
                                            className={cn(
                                                "flex items-center gap-2 p-2 rounded-lg border transition-all",
                                                localFilters.piliers?.includes(pilier.id)
                                                    ? "border-primary bg-primary/10"
                                                    : "border-gray-200 hover:border-primary/50"
                                            )}
                                        >
                                            <div
                                                className="w-3 h-3 rounded-full flex-shrink-0"
                                                style={{ backgroundColor: pilier.couleur }}
                                            />
                                            <span className="text-xs font-medium truncate">
                                                {pilier.nom}
                                            </span>
                                            {localFilters.piliers?.includes(pilier.id) && (
                                                <CheckIcon className="h-3 w-3 text-primary ml-auto flex-shrink-0" />
                                            )}
                                        </button>
                                    ))}
                                </div>
                            </div>
                        )}

                        {/* Type d'expression */}
                        <div>
                            <h3 className="text-sm font-medium text-foreground mb-3">
                                Type d'expression
                            </h3>
                            <div className="space-y-2">
                                {[
                                    { value: "probleme", label: "Problème", color: "destructive" },
                                    { value: "satisfaction", label: "Satisfaction", color: "success" },
                                    { value: "idee", label: "Idée", color: "info" },
                                    { value: "question", label: "Question", color: "warning" },
                                ].map(type => (
                                    <button
                                        key={type.value}
                                        onClick={() => toggleArrayFilter("typeExpression", type.value)}
                                        className={cn(
                                            "w-full flex items-center justify-between p-3 rounded-lg border transition-all",
                                            localFilters.typeExpression?.includes(type.value)
                                                ? "border-primary bg-primary/10"
                                                : "border-gray-200 hover:border-primary/50"
                                        )}
                                    >
                                        <div className="flex items-center gap-2">
                                            <Badge variant={type.color as any} size="sm">
                                                {type.label}
                                            </Badge>
                                        </div>
                                        {localFilters.typeExpression?.includes(type.value) && (
                                            <CheckIcon className="h-4 w-4 text-primary" />
                                        )}
                                    </button>
                                ))}
                            </div>
                        </div>

                        {/* Urgence */}
                        <div>
                            <h3 className="text-sm font-medium text-foreground mb-3">
                                Niveau d'urgence
                            </h3>
                            <div className="flex gap-2">
                                {[1, 2, 3, 4, 5].map(level => (
                                    <button
                                        key={level}
                                        onClick={() => toggleArrayFilter("urgence", level)}
                                        className={cn(
                                            "flex-1 py-2 px-3 rounded-lg border font-medium transition-all",
                                            localFilters.urgence?.includes(level)
                                                ? "border-primary bg-primary text-primary-foreground"
                                                : "border-gray-200 hover:border-primary/50"
                                        )}
                                    >
                                        {level}
                                    </button>
                                ))}
                            </div>
                        </div>

                        {/* Statut */}
                        <div>
                            <h3 className="text-sm font-medium text-foreground mb-3">
                                Statut
                            </h3>
                            <div className="space-y-2">
                                {[
                                    { value: "brouillon", label: "Brouillon" },
                                    { value: "en_moderation", label: "En modération" },
                                    { value: "publie", label: "Publié" },
                                    { value: "rejete", label: "Rejeté" },
                                    { value: "resolu", label: "Résolu" },
                                    { value: "archive", label: "Archivé" },
                                ].map(status => (
                                    <button
                                        key={status.value}
                                        onClick={() => toggleArrayFilter("statut", status.value)}
                                        className={cn(
                                            "w-full flex items-center justify-between p-3 rounded-lg border transition-all",
                                            localFilters.statut?.includes(status.value)
                                                ? "border-primary bg-primary/10"
                                                : "border-gray-200 hover:border-primary/50"
                                        )}
                                    >
                                        <span className="text-sm">{status.label}</span>
                                        {localFilters.statut?.includes(status.value) && (
                                            <CheckIcon className="h-4 w-4 text-primary" />
                                        )}
                                    </button>
                                ))}
                            </div>
                        </div>

                        {/* Date Range */}
                        <div>
                            <h3 className="text-sm font-medium text-foreground mb-3">
                                Période
                            </h3>
                            <div className="space-y-2">
                                <input
                                    type="date"
                                    className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                                    placeholder="Date de début"
                                    onChange={(e) => {
                                        const date = e.target.value ? new Date(e.target.value) : undefined;
                                        handleFilterChange("dateRange", {
                                            ...localFilters.dateRange,
                                            start: date,
                                        });
                                    }}
                                />
                                <input
                                    type="date"
                                    className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                                    placeholder="Date de fin"
                                    onChange={(e) => {
                                        const date = e.target.value ? new Date(e.target.value) : undefined;
                                        handleFilterChange("dateRange", {
                                            ...localFilters.dateRange,
                                            end: date,
                                        });
                                    }}
                                />
                            </div>
                        </div>
                    </div>

                    {/* Footer */}
                    <div className="border-t p-6 space-y-3">
                        <div className="flex gap-3">
                            <Button
                                variant="outline"
                                fullWidth
                                onClick={handleReset}
                                disabled={getActiveFilterCount() === 0}
                            >
                                Réinitialiser
                            </Button>
                            <Button
                                variant="primary"
                                fullWidth
                                onClick={handleApply}
                            >
                                Appliquer les filtres
                            </Button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default AdvancedFilters;