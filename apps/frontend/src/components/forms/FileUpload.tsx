"use client";

import React, { useCallback, useState } from "react";
import { useDropzone } from "react-dropzone";
import { cn } from "@/utils";
import Button from "@/components/ui/Button";
import {
    CloudArrowUpIcon,
    DocumentIcon,
    PhotoIcon,
    XMarkIcon,
    EyeIcon,
} from "@heroicons/react/24/outline";

export interface UploadedFile {
    id: string;
    file: File;
    preview?: string;
    url?: string;
    type: "image" | "document" | "video";
    uploaded: boolean;
    error?: string;
}

interface FileUploadProps {
    onFilesChange: (files: UploadedFile[]) => void;
    acceptedTypes?: string[];
    maxFiles?: number;
    maxSize?: number; // in MB
    className?: string;
    disabled?: boolean;
    multiple?: boolean;
}

const FileUpload: React.FC<FileUploadProps> = ({
    onFilesChange,
    acceptedTypes = ["image/*", "application/pdf", ".doc", ".docx"],
    maxFiles = 5,
    maxSize = 10, // 10MB default
    className,
    disabled = false,
    multiple = true,
}) => {
    const [files, setFiles] = useState<UploadedFile[]>([]);
    const [uploading, setUploading] = useState(false);

    const generateFileId = () => Math.random().toString(36).substr(2, 9);

    const getFileType = (file: File): "image" | "document" | "video" => {
        if (file.type.startsWith("image/")) return "image";
        if (file.type.startsWith("video/")) return "video";
        return "document";
    };

    const createPreview = (file: File): Promise<string | undefined> => {
        return new Promise((resolve) => {
            if (file.type.startsWith("image/")) {
                const reader = new FileReader();
                reader.onload = () => resolve(reader.result as string);
                reader.onerror = () => resolve(undefined);
                reader.readAsDataURL(file);
            } else {
                resolve(undefined);
            }
        });
    };

    const uploadFile = async (file: UploadedFile): Promise<UploadedFile> => {
        // Mock upload simulation - replace with actual API call
        await new Promise((resolve) => setTimeout(resolve, 1000 + Math.random() * 2000));

        // Simulate some upload failures
        if (Math.random() < 0.1) {
            throw new Error("Upload failed. Please try again.");
        }

        return {
            ...file,
            uploaded: true,
            url: `https://api.pillarscan.fr/uploads/${file.id}-${file.file.name}`,
        };
    };

    const onDrop = useCallback(
        async (acceptedFiles: File[], rejectedFiles: any[]) => {
            if (disabled) return;

            // Handle rejected files
            if (rejectedFiles.length > 0) {
                console.warn("Some files were rejected:", rejectedFiles);
            }

            // Check if adding these files would exceed maxFiles
            if (files.length + acceptedFiles.length > maxFiles) {
                alert(`Vous ne pouvez télécharger que ${maxFiles} fichiers maximum.`);
                return;
            }

            // Create initial file objects
            const newFiles: UploadedFile[] = [];
            for (const file of acceptedFiles) {
                // Check file size
                if (file.size > maxSize * 1024 * 1024) {
                    alert(`Le fichier ${file.name} est trop volumineux (max ${maxSize}MB).`);
                    continue;
                }

                const fileId = generateFileId();
                const preview = await createPreview(file);

                newFiles.push({
                    id: fileId,
                    file,
                    preview,
                    type: getFileType(file),
                    uploaded: false,
                });
            }

            const updatedFiles = [...files, ...newFiles];
            setFiles(updatedFiles);
            onFilesChange(updatedFiles);

            // Start uploading files
            setUploading(true);
            try {
                const uploadPromises = newFiles.map(async (fileObj) => {
                    try {
                        const uploaded = await uploadFile(fileObj);
                        setFiles((prev) => prev.map((f) => (f.id === fileObj.id ? uploaded : f)));
                        return uploaded;
                    } catch (error) {
                        const failedFile = {
                            ...fileObj,
                            uploaded: false,
                            error: error instanceof Error ? error.message : "Upload failed",
                        };
                        setFiles((prev) => prev.map((f) => (f.id === fileObj.id ? failedFile : f)));
                        return failedFile;
                    }
                });

                const results = await Promise.all(uploadPromises);
                onFilesChange([...files, ...results]);
            } finally {
                setUploading(false);
            }
        },
        [files, maxFiles, maxSize, disabled, onFilesChange],
    );

    const { getRootProps, getInputProps, isDragActive } = useDropzone({
        onDrop,
        accept: acceptedTypes.reduce((acc, type) => ({ ...acc, [type]: [] }), {}),
        multiple,
        disabled: disabled || uploading,
    });

    const removeFile = (fileId: string) => {
        const updatedFiles = files.filter((f) => f.id !== fileId);
        setFiles(updatedFiles);
        onFilesChange(updatedFiles);
    };

    const retryUpload = async (fileId: string) => {
        const fileObj = files.find((f) => f.id === fileId);
        if (!fileObj) return;

        setUploading(true);
        try {
            const uploaded = await uploadFile(fileObj);
            const updatedFiles = files.map((f) => (f.id === fileId ? uploaded : f));
            setFiles(updatedFiles);
            onFilesChange(updatedFiles);
        } catch (error) {
            const failedFile = {
                ...fileObj,
                error: error instanceof Error ? error.message : "Upload failed",
            };
            const updatedFiles = files.map((f) => (f.id === fileId ? failedFile : f));
            setFiles(updatedFiles);
            onFilesChange(updatedFiles);
        } finally {
            setUploading(false);
        }
    };

    const getFileIcon = (type: UploadedFile["type"]) => {
        switch (type) {
            case "image":
                return <PhotoIcon className="h-8 w-8" />;
            case "video":
                return <PhotoIcon className="h-8 w-8" />;
            default:
                return <DocumentIcon className="h-8 w-8" />;
        }
    };

    const formatFileSize = (bytes: number) => {
        if (bytes === 0) return "0 Bytes";
        const k = 1024;
        const sizes = ["Bytes", "KB", "MB", "GB"];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
    };

    return (
        <div className={cn("space-y-4", className)}>
            {/* Drop Zone */}
            <div
                {...getRootProps()}
                className={cn(
                    "border-2 border-dashed rounded-xl p-8 text-center cursor-pointer transition-all duration-200",
                    isDragActive
                        ? "border-primary bg-primary/5"
                        : "border-gray-200 hover:border-primary/50 hover:bg-secondary/30",
                    disabled && "opacity-50 cursor-not-allowed",
                    files.length >= maxFiles && "opacity-50 cursor-not-allowed",
                )}
            >
                <input {...getInputProps()} />
                <CloudArrowUpIcon className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />

                {isDragActive ? (
                    <p className="text-lg font-medium text-primary">Déposez vos fichiers ici...</p>
                ) : (
                    <div>
                        <p className="text-lg font-medium text-foreground mb-2">
                            Glissez-déposez vos fichiers ici
                        </p>
                        <p className="text-sm text-muted-foreground mb-4">
                            ou cliquez pour sélectionner
                        </p>
                        <Button
                            variant="outline"
                            size="sm"
                            disabled={disabled || files.length >= maxFiles}
                        >
                            {files.length >= maxFiles ? "Limite atteinte" : "Choisir des fichiers"}
                        </Button>
                    </div>
                )}

                <div className="mt-4 text-xs text-muted-foreground">
                    <p>
                        Maximum {maxFiles} fichiers • {maxSize}MB par fichier
                    </p>
                    <p>Formats acceptés: {acceptedTypes.join(", ")}</p>
                </div>
            </div>

            {/* File List */}
            {files.length > 0 && (
                <div className="space-y-3">
                    <h4 className="text-sm font-medium text-foreground">
                        Fichiers ({files.length}/{maxFiles})
                    </h4>

                    <div className="space-y-2">
                        {files.map((file) => (
                            <div
                                key={file.id}
                                className="flex items-center gap-3 p-3 bg-secondary/30 rounded-lg border"
                            >
                                {/* File Icon/Preview */}
                                <div className="flex-shrink-0">
                                    {file.preview ? (
                                        <div className="relative">
                                            <img
                                                src={file.preview}
                                                alt={file.file.name}
                                                className="h-12 w-12 object-cover rounded-lg"
                                            />
                                            <button
                                                onClick={() => window.open(file.preview, "_blank")}
                                                className="absolute -top-1 -right-1 h-5 w-5 bg-primary rounded-full flex items-center justify-center"
                                            >
                                                <EyeIcon className="h-3 w-3 text-white" />
                                            </button>
                                        </div>
                                    ) : (
                                        <div className="h-12 w-12 bg-secondary rounded-lg flex items-center justify-center text-muted-foreground">
                                            {getFileIcon(file.type)}
                                        </div>
                                    )}
                                </div>

                                {/* File Info */}
                                <div className="flex-1 min-w-0">
                                    <p className="text-sm font-medium text-foreground truncate">
                                        {file.file.name}
                                    </p>
                                    <p className="text-xs text-muted-foreground">
                                        {formatFileSize(file.file.size)}
                                    </p>

                                    {/* Upload Status */}
                                    {file.uploaded ? (
                                        <p className="text-xs text-success">✓ Téléchargé</p>
                                    ) : file.error ? (
                                        <div className="flex items-center gap-2">
                                            <p className="text-xs text-destructive">
                                                ✗ {file.error}
                                            </p>
                                            <Button
                                                variant="ghost"
                                                size="xs"
                                                onClick={() => retryUpload(file.id)}
                                                disabled={uploading}
                                            >
                                                Réessayer
                                            </Button>
                                        </div>
                                    ) : (
                                        <p className="text-xs text-warning">⏳ Téléchargement...</p>
                                    )}
                                </div>

                                {/* Remove Button */}
                                <button
                                    onClick={() => removeFile(file.id)}
                                    className="flex-shrink-0 p-1 text-muted-foreground hover:text-destructive transition-colors"
                                    disabled={uploading}
                                >
                                    <XMarkIcon className="h-4 w-4" />
                                </button>
                            </div>
                        ))}
                    </div>
                </div>
            )}
        </div>
    );
};

export default FileUpload;
