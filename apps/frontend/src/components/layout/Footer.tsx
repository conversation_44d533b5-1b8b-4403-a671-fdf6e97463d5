import React from "react";
import Link from "next/link";

const Footer: React.FC = () => {
    const currentYear = new Date().getFullYear();

    const footerLinks = {
        platform: [
            { name: "À propos", href: "/about" },
            { name: "Comment ça marche", href: "/how-it-works" },
            { name: "Piliers", href: "/piliers" },
            { name: "Statistiques", href: "/stats" },
        ],
        legal: [
            { name: "Mentions légales", href: "/legal" },
            { name: "Politique de confidentialité", href: "/privacy" },
            { name: "Conditions d'utilisation", href: "/terms" },
            { name: "Cook<PERSON>", href: "/cookies" },
        ],
        support: [
            { name: "Centre d'aide", href: "/help" },
            { name: "Contact", href: "/contact" },
            { name: "Signaler un problème", href: "/report" },
            { name: "API", href: "/api-docs" },
        ],
    };

    return (
        <footer className="bg-gray-50 border-t">
            <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                <div className="py-12">
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                        {/* Brand */}
                        <div className="col-span-1">
                            <div className="flex items-center space-x-2 mb-4">
                                <div className="h-8 w-8 rounded-lg bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                                    <span className="text-white font-bold text-sm">PS</span>
                                </div>
                                <span className="text-xl font-bold text-gray-900">PillarScan</span>
                            </div>
                            <p className="text-sm text-gray-600 mb-4">
                                La plateforme citoyenne pour exprimer et suivre les préoccupations
                                sur les 12 piliers de la société française.
                            </p>
                            <div className="flex space-x-4">
                                <a
                                    href="https://twitter.com/pillarscan"
                                    className="text-gray-400 hover:text-gray-500"
                                    target="_blank"
                                    rel="noopener noreferrer"
                                >
                                    <span className="sr-only">Twitter</span>
                                    <svg
                                        className="h-5 w-5"
                                        fill="currentColor"
                                        viewBox="0 0 20 20"
                                    >
                                        <path d="M6.29 18.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0020 3.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.073 4.073 0 01.8 7.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 010 16.407a11.616 11.616 0 006.29 1.84" />
                                    </svg>
                                </a>
                                <a
                                    href="https://github.com/pillarscan"
                                    className="text-gray-400 hover:text-gray-500"
                                    target="_blank"
                                    rel="noopener noreferrer"
                                >
                                    <span className="sr-only">GitHub</span>
                                    <svg
                                        className="h-5 w-5"
                                        fill="currentColor"
                                        viewBox="0 0 20 20"
                                    >
                                        <path
                                            fillRule="evenodd"
                                            d="M10 0C4.477 0 0 4.484 0 10.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0110 4.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.203 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.942.359.31.678.921.678 1.856 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0020 10.017C20 4.484 15.522 0 10 0z"
                                            clipRule="evenodd"
                                        />
                                    </svg>
                                </a>
                            </div>
                        </div>

                        {/* Platform */}
                        <div>
                            <h3 className="text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4">
                                Plateforme
                            </h3>
                            <ul className="space-y-3">
                                {footerLinks.platform.map((link) => (
                                    <li key={link.name}>
                                        <Link
                                            href={link.href}
                                            className="text-sm text-gray-600 hover:text-gray-900 transition-colors"
                                        >
                                            {link.name}
                                        </Link>
                                    </li>
                                ))}
                            </ul>
                        </div>

                        {/* Legal */}
                        <div>
                            <h3 className="text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4">
                                Légal
                            </h3>
                            <ul className="space-y-3">
                                {footerLinks.legal.map((link) => (
                                    <li key={link.name}>
                                        <Link
                                            href={link.href}
                                            className="text-sm text-gray-600 hover:text-gray-900 transition-colors"
                                        >
                                            {link.name}
                                        </Link>
                                    </li>
                                ))}
                            </ul>
                        </div>

                        {/* Support */}
                        <div>
                            <h3 className="text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4">
                                Support
                            </h3>
                            <ul className="space-y-3">
                                {footerLinks.support.map((link) => (
                                    <li key={link.name}>
                                        <Link
                                            href={link.href}
                                            className="text-sm text-gray-600 hover:text-gray-900 transition-colors"
                                        >
                                            {link.name}
                                        </Link>
                                    </li>
                                ))}
                            </ul>
                        </div>
                    </div>
                </div>

                <div className="border-t border-gray-200 py-6">
                    <div className="flex flex-col md:flex-row justify-between items-center">
                        <p className="text-sm text-gray-600">
                            © {currentYear} PillarScan. Tous droits réservés.
                        </p>
                        <p className="text-sm text-gray-600 mt-2 md:mt-0">
                            Fait avec ❤️ pour la démocratie participative
                        </p>
                    </div>
                </div>
            </div>
        </footer>
    );
};

export default Footer;
