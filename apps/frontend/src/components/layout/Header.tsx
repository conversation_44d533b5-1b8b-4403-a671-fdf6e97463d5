"use client";

import React from "react";
import Link from "next/link";
import { useAuthStore } from "@/stores/authStore";
import Button from "@/components/ui/Button";
import NotificationCenter from "@/components/ui/NotificationCenter";
import { UserIcon, Bars3Icon, XMarkIcon } from "@heroicons/react/24/outline";
import { useState } from "react";

const Header: React.FC = () => {
    const { isAuthenticated, user, profile, logout } = useAuthStore();
    const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

    const handleLogout = () => {
        logout();
        setMobileMenuOpen(false);
    };

    const getNavigation = () => {
        const baseNav = [
            { name: "Accueil", href: "/" },
            { name: "Expressions", href: "/expressions" },
            { name: "Piliers", href: "/piliers" },
            { name: "<PERSON><PERSON>", href: "/carte" },
        ];

        // Add admin/validator specific links
        if (profile?.role === "super_admin" || profile?.role === "validateur") {
            baseNav.push(
                { name: "Analytics", href: "/analytics" },
                { name: "Modération", href: "/moderation" },
            );
        }

        return baseNav;
    };

    const navigation = getNavigation();

    return (
        <header className="bg-white shadow-sm border-b">
            <nav className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8" aria-label="Top">
                <div className="flex w-full items-center justify-between py-4">
                    {/* Logo */}
                    <div className="flex items-center">
                        <Link href="/" className="flex items-center space-x-2">
                            <div className="h-8 w-8 rounded-lg bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                                <span className="text-white font-bold text-sm">PS</span>
                            </div>
                            <span className="text-xl font-bold text-gray-900">PillarScan</span>
                        </Link>
                    </div>

                    {/* Desktop Navigation */}
                    <div className="hidden md:flex md:items-center md:space-x-6">
                        {navigation.map((item) => (
                            <Link
                                key={item.name}
                                href={item.href}
                                className="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium transition-colors"
                            >
                                {item.name}
                            </Link>
                        ))}
                    </div>

                    {/* Desktop Auth */}
                    <div className="hidden md:flex md:items-center md:space-x-4">
                        {isAuthenticated ? (
                            <div className="flex items-center space-x-4">
                                <NotificationCenter />
                                <div className="flex items-center space-x-2">
                                    <UserIcon className="h-5 w-5 text-gray-400" />
                                    <span className="text-sm text-gray-700">
                                        {profile?.nom || user?.username}
                                    </span>
                                </div>
                                <Link href="/dashboard">
                                    <Button variant="outline" size="sm">
                                        Dashboard
                                    </Button>
                                </Link>
                                <Button variant="ghost" size="sm" onClick={handleLogout}>
                                    Déconnexion
                                </Button>
                            </div>
                        ) : (
                            <div className="flex items-center space-x-4">
                                <Link href="/auth/login">
                                    <Button variant="ghost" size="sm">
                                        Connexion
                                    </Button>
                                </Link>
                                <Link href="/auth/register">
                                    <Button variant="primary" size="sm">
                                        S'inscrire
                                    </Button>
                                </Link>
                            </div>
                        )}
                    </div>

                    {/* Mobile menu button */}
                    <div className="md:hidden">
                        <button
                            type="button"
                            className="text-gray-400 hover:text-gray-500"
                            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
                        >
                            <span className="sr-only">Ouvrir le menu</span>
                            {mobileMenuOpen ? (
                                <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                            ) : (
                                <Bars3Icon className="h-6 w-6" aria-hidden="true" />
                            )}
                        </button>
                    </div>
                </div>

                {/* Mobile menu */}
                {mobileMenuOpen && (
                    <div className="md:hidden">
                        <div className="space-y-1 pb-3 pt-2">
                            {navigation.map((item) => (
                                <Link
                                    key={item.name}
                                    href={item.href}
                                    className="block px-3 py-2 text-base font-medium text-gray-600 hover:text-gray-900"
                                    onClick={() => setMobileMenuOpen(false)}
                                >
                                    {item.name}
                                </Link>
                            ))}

                            <div className="border-t border-gray-200 pt-4">
                                {isAuthenticated ? (
                                    <div className="space-y-2">
                                        <div className="px-3 py-2">
                                            <div className="flex items-center space-x-2">
                                                <UserIcon className="h-5 w-5 text-gray-400" />
                                                <span className="text-sm text-gray-700">
                                                    {profile?.nom || user?.username}
                                                </span>
                                            </div>
                                        </div>
                                        <Link
                                            href="/dashboard"
                                            className="block px-3 py-2 text-base font-medium text-gray-600 hover:text-gray-900"
                                            onClick={() => setMobileMenuOpen(false)}
                                        >
                                            Dashboard
                                        </Link>
                                        <button
                                            onClick={handleLogout}
                                            className="block w-full text-left px-3 py-2 text-base font-medium text-gray-600 hover:text-gray-900"
                                        >
                                            Déconnexion
                                        </button>
                                    </div>
                                ) : (
                                    <div className="space-y-2">
                                        <Link
                                            href="/auth/login"
                                            className="block px-3 py-2 text-base font-medium text-gray-600 hover:text-gray-900"
                                            onClick={() => setMobileMenuOpen(false)}
                                        >
                                            Connexion
                                        </Link>
                                        <Link
                                            href="/auth/register"
                                            className="block px-3 py-2 text-base font-medium text-blue-600 hover:text-blue-700"
                                            onClick={() => setMobileMenuOpen(false)}
                                        >
                                            S'inscrire
                                        </Link>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                )}
            </nav>
        </header>
    );
};

export default Header;
