"use client";

import React, { useEffect } from "react";
import { useAuthStore } from "@/stores/authStore";
import Header from "./Header";
import Footer from "./Footer";
import RealtimeProvider from "@/components/providers/RealtimeProvider";
import { Toaster } from "react-hot-toast";

interface LayoutProps {
    children: React.ReactNode;
    showHeader?: boolean;
    showFooter?: boolean;
    className?: string;
}

const Layout: React.FC<LayoutProps> = ({
    children,
    showHeader = true,
    showFooter = true,
    className = "",
}) => {
    const { loadUser, token } = useAuthStore();

    // Load user on app start if token exists
    useEffect(() => {
        if (token) {
            loadUser();
        }
    }, [token, loadUser]);

    return (
        <RealtimeProvider>
            <div className="min-h-screen flex flex-col bg-gray-50">
                {showHeader && <Header />}

                <main className={`flex-1 ${className}`}>{children}</main>

                {showFooter && <Footer />}

                {/* Toast notifications */}
                <Toaster
                    position="top-right"
                    toastOptions={{
                        duration: 4000,
                        style: {
                            background: "#363636",
                            color: "#fff",
                        },
                        success: {
                            duration: 3000,
                            iconTheme: {
                                primary: "#4ade80",
                                secondary: "#fff",
                            },
                        },
                        error: {
                            duration: 5000,
                            iconTheme: {
                                primary: "#ef4444",
                                secondary: "#fff",
                            },
                        },
                    }}
                />
            </div>
        </RealtimeProvider>
    );
};

export default Layout;
