"use client";

import React, { useState, useCallback, useEffect } from "react";
import { cn } from "@/utils";
import Input from "@/components/ui/Input";
import Button from "@/components/ui/Button";
import { Card } from "@/components/ui/Card";
import { MapPinIcon, MagnifyingGlassIcon, CheckIcon, XMarkIcon } from "@heroicons/react/24/outline";

interface Location {
    id: string;
    name: string;
    fullName: string;
    coordinates: {
        lat: number;
        lng: number;
    };
    type: "city" | "department" | "region" | "address";
    postalCode?: string;
    department?: string;
    region?: string;
}

interface LocationPickerProps {
    onLocationSelect: (location: Location | null) => void;
    defaultValue?: Location;
    placeholder?: string;
    className?: string;
    required?: boolean;
    disabled?: boolean;
}

const LocationPicker: React.FC<LocationPickerProps> = ({
    onLocationSelect,
    defaultValue,
    placeholder = "Entrez votre ville ou adresse...",
    className,
    required = false,
    disabled = false,
}) => {
    const [query, setQuery] = useState(defaultValue?.name || "");
    const [suggestions, setSuggestions] = useState<Location[]>([]);
    const [selectedLocation, setSelectedLocation] = useState<Location | null>(defaultValue || null);
    const [isLoading, setIsLoading] = useState(false);
    const [showSuggestions, setShowSuggestions] = useState(false);
    const [error, setError] = useState<string | null>(null);

    // Debounced search for locations
    useEffect(() => {
        if (query.length < 3) {
            setSuggestions([]);
            return;
        }

        const timer = setTimeout(() => {
            searchLocations(query);
        }, 300);

        return () => clearTimeout(timer);
    }, [query]);

    const searchLocations = async (searchQuery: string) => {
        if (!searchQuery.trim()) return;

        setIsLoading(true);
        setError(null);

        try {
            // Mock implementation - replace with actual geocoding API
            // This could be integrated with French government API or Google Places API
            const mockResults = await mockLocationSearch(searchQuery);
            setSuggestions(mockResults);
            setShowSuggestions(true);
        } catch (error) {
            console.error("Location search error:", error);
            setError("Erreur lors de la recherche de lieu");
            setSuggestions([]);
        } finally {
            setIsLoading(false);
        }
    };

    const handleLocationSelect = (location: Location) => {
        setSelectedLocation(location);
        setQuery(location.name);
        setShowSuggestions(false);
        onLocationSelect(location);
    };

    const handleClear = () => {
        setQuery("");
        setSelectedLocation(null);
        setSuggestions([]);
        setShowSuggestions(false);
        onLocationSelect(null);
    };

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        setQuery(value);

        if (value !== selectedLocation?.name) {
            setSelectedLocation(null);
            onLocationSelect(null);
        }
    };

    // Use browser geolocation API
    const getCurrentLocation = () => {
        if (!navigator.geolocation) {
            setError("La géolocalisation n'est pas supportée par votre navigateur");
            return;
        }

        setIsLoading(true);
        navigator.geolocation.getCurrentPosition(
            async (position) => {
                try {
                    // Reverse geocoding to get location name from coordinates
                    const location = await reverseGeocode(
                        position.coords.latitude,
                        position.coords.longitude,
                    );
                    handleLocationSelect(location);
                } catch (error) {
                    setError("Impossible de déterminer votre localisation");
                } finally {
                    setIsLoading(false);
                }
            },
            (error) => {
                setError("Accès à la localisation refusé");
                setIsLoading(false);
            },
            {
                enableHighAccuracy: true,
                timeout: 10000,
                maximumAge: 300000, // 5 minutes
            },
        );
    };

    const getLocationIcon = (type: Location["type"]) => {
        switch (type) {
            case "address":
                return "🏠";
            case "city":
                return "🏙️";
            case "department":
                return "📍";
            case "region":
                return "🗺️";
            default:
                return "📍";
        }
    };

    return (
        <div className={cn("relative", className)}>
            <div className="relative">
                <MapPinIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />

                <Input
                    type="text"
                    value={query}
                    onChange={handleInputChange}
                    onFocus={() => suggestions.length > 0 && setShowSuggestions(true)}
                    onBlur={() => {
                        // Delay to allow clicking on suggestions
                        setTimeout(() => setShowSuggestions(false), 200);
                    }}
                    placeholder={placeholder}
                    className={cn(
                        "pl-10 pr-20",
                        selectedLocation && "border-success",
                        error && "border-destructive",
                    )}
                    required={required}
                    disabled={disabled}
                />

                <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex gap-1">
                    {selectedLocation && (
                        <button
                            onClick={handleClear}
                            className="p-1 text-muted-foreground hover:text-foreground transition-colors"
                            disabled={disabled}
                        >
                            <XMarkIcon className="h-4 w-4" />
                        </button>
                    )}

                    <Button
                        variant="ghost"
                        size="xs"
                        onClick={getCurrentLocation}
                        disabled={disabled || isLoading}
                        className="h-6 w-6 p-0"
                        title="Utiliser ma position actuelle"
                    >
                        🎯
                    </Button>
                </div>
            </div>

            {/* Loading indicator */}
            {isLoading && (
                <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                    <div className="animate-spin h-4 w-4 border-2 border-primary border-t-transparent rounded-full"></div>
                </div>
            )}

            {/* Error message */}
            {error && <p className="mt-1 text-xs text-destructive">{error}</p>}

            {/* Selected location display */}
            {selectedLocation && (
                <div className="mt-2 p-2 bg-success/10 border border-success/20 rounded-lg flex items-center gap-2">
                    <CheckIcon className="h-4 w-4 text-success flex-shrink-0" />
                    <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-success truncate">
                            {selectedLocation.fullName}
                        </p>
                        <p className="text-xs text-success/70">
                            {selectedLocation.coordinates.lat.toFixed(6)},{" "}
                            {selectedLocation.coordinates.lng.toFixed(6)}
                        </p>
                    </div>
                </div>
            )}

            {/* Suggestions dropdown */}
            {showSuggestions && suggestions.length > 0 && (
                <Card className="absolute z-10 w-full mt-1 max-h-64 overflow-y-auto shadow-lg">
                    <div className="py-1">
                        {suggestions.map((location) => (
                            <button
                                key={location.id}
                                onClick={() => handleLocationSelect(location)}
                                className="w-full px-3 py-2.5 text-left hover:bg-secondary/50 transition-colors flex items-center gap-3"
                            >
                                <span className="text-lg">{getLocationIcon(location.type)}</span>
                                <div className="flex-1 min-w-0">
                                    <p className="text-sm font-medium text-foreground truncate">
                                        {location.name}
                                    </p>
                                    <p className="text-xs text-muted-foreground truncate">
                                        {location.fullName}
                                    </p>
                                </div>
                                <span className="text-xs text-muted-foreground capitalize bg-secondary px-2 py-1 rounded">
                                    {location.type}
                                </span>
                            </button>
                        ))}
                    </div>
                </Card>
            )}
        </div>
    );
};

// Mock location search function - replace with actual API
const mockLocationSearch = async (query: string): Promise<Location[]> => {
    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 300));

    const mockData: Location[] = [
        {
            id: "1",
            name: "Paris",
            fullName: "Paris, Île-de-France, France",
            coordinates: { lat: 48.8566, lng: 2.3522 },
            type: "city",
            postalCode: "75000",
            department: "Paris",
            region: "Île-de-France",
        },
        {
            id: "2",
            name: "Lyon",
            fullName: "Lyon, Auvergne-Rhône-Alpes, France",
            coordinates: { lat: 45.764, lng: 4.8357 },
            type: "city",
            postalCode: "69000",
            department: "Rhône",
            region: "Auvergne-Rhône-Alpes",
        },
        {
            id: "3",
            name: "Marseille",
            fullName: "Marseille, Bouches-du-Rhône, Provence-Alpes-Côte d'Azur, France",
            coordinates: { lat: 43.2965, lng: 5.3698 },
            type: "city",
            postalCode: "13000",
            department: "Bouches-du-Rhône",
            region: "Provence-Alpes-Côte d'Azur",
        },
        {
            id: "4",
            name: "Toulouse",
            fullName: "Toulouse, Haute-Garonne, Occitanie, France",
            coordinates: { lat: 43.6047, lng: 1.4442 },
            type: "city",
            postalCode: "31000",
            department: "Haute-Garonne",
            region: "Occitanie",
        },
        {
            id: "5",
            name: "Nice",
            fullName: "Nice, Alpes-Maritimes, Provence-Alpes-Côte d'Azur, France",
            coordinates: { lat: 43.7102, lng: 7.262 },
            type: "city",
            postalCode: "06000",
            department: "Alpes-Maritimes",
            region: "Provence-Alpes-Côte d'Azur",
        },
    ];

    return mockData.filter(
        (location) =>
            location.name.toLowerCase().includes(query.toLowerCase()) ||
            location.fullName.toLowerCase().includes(query.toLowerCase()),
    );
};

// Mock reverse geocoding function
const reverseGeocode = async (lat: number, lng: number): Promise<Location> => {
    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 500));

    // Mock implementation - in real app, use actual reverse geocoding API
    return {
        id: "current",
        name: "Ma position actuelle",
        fullName: `Position actuelle (${lat.toFixed(4)}, ${lng.toFixed(4)})`,
        coordinates: { lat, lng },
        type: "address",
    };
};

export default LocationPicker;
