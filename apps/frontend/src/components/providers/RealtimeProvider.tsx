"use client";

import React, { useEffect } from "react";
import { useAuthStore } from "@/stores/authStore";
import { useRealtime } from "@/lib/realtime";

interface RealtimeProviderProps {
    children: React.ReactNode;
}

export default function RealtimeProvider({ children }: RealtimeProviderProps) {
    const { isAuthenticated, user } = useAuthStore();
    const { connect, disconnect } = useRealtime();

    useEffect(() => {
        if (isAuthenticated && user) {
            // Get token from localStorage or auth store
            const token = localStorage.getItem("token") || "";

            console.log("Connecting to realtime service...");
            connect(user.id, token);

            // Cleanup on unmount or auth change
            return () => {
                console.log("Disconnecting from realtime service...");
                disconnect();
            };
        } else {
            disconnect();
        }
    }, [isAuthenticated, user, connect, disconnect]);

    return <>{children}</>;
}
