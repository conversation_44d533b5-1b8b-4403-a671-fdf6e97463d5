"use client";

import React, { useState, useCallback, useEffect } from "react";
import { useRouter } from "next/navigation";
import { cn } from "@/utils";
import Input from "@/components/ui/Input";
import Button from "@/components/ui/Button";
import Badge from "@/components/ui/Badge";
import {
    MagnifyingGlassIcon,
    XMarkIcon,
    FunnelIcon,
    ClockIcon,
} from "@heroicons/react/24/outline";

interface SearchBarProps {
    placeholder?: string;
    defaultValue?: string;
    onSearch?: (query: string) => void;
    onClear?: () => void;
    showFilters?: boolean;
    onFilterClick?: () => void;
    filterCount?: number;
    className?: string;
    autoFocus?: boolean;
    searchInUrl?: boolean;
    recentSearches?: string[];
    suggestions?: string[];
}

const SearchBar: React.FC<SearchBarProps> = ({
    placeholder = "Rechercher des expressions, piliers, lieux...",
    defaultValue = "",
    onSearch,
    onClear,
    showFilters = true,
    onFilterClick,
    filterCount = 0,
    className,
    autoFocus = false,
    searchInUrl = true,
    recentSearches = [],
    suggestions = [],
}) => {
    const router = useRouter();
    const [query, setQuery] = useState(defaultValue);
    const [isLoading, setIsLoading] = useState(false);
    const [showSuggestions, setShowSuggestions] = useState(false);
    const [selectedSuggestionIndex, setSelectedSuggestionIndex] = useState(-1);

    // Debounce search
    useEffect(() => {
        const timer = setTimeout(() => {
            if (query !== defaultValue) {
                handleSearch(query);
            }
        }, 300);

        return () => clearTimeout(timer);
    }, [query]);

    const handleSearch = useCallback((searchQuery: string) => {
        if (searchInUrl) {
            const params = new URLSearchParams(window.location.search);
            if (searchQuery) {
                params.set("q", searchQuery);
            } else {
                params.delete("q");
            }
            router.push(`?${params.toString()}`);
        }
        
        onSearch?.(searchQuery);
    }, [router, searchInUrl, onSearch]);

    const handleClear = () => {
        setQuery("");
        setShowSuggestions(false);
        onClear?.();
        
        if (searchInUrl) {
            const params = new URLSearchParams(window.location.search);
            params.delete("q");
            router.push(`?${params.toString()}`);
        }
    };

    const handleSuggestionClick = (suggestion: string) => {
        setQuery(suggestion);
        setShowSuggestions(false);
        handleSearch(suggestion);
    };

    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
        const allSuggestions = [...recentSearches, ...suggestions];
        
        if (e.key === "ArrowDown") {
            e.preventDefault();
            setSelectedSuggestionIndex(prev => 
                prev < allSuggestions.length - 1 ? prev + 1 : prev
            );
        } else if (e.key === "ArrowUp") {
            e.preventDefault();
            setSelectedSuggestionIndex(prev => prev > -1 ? prev - 1 : -1);
        } else if (e.key === "Enter" && selectedSuggestionIndex >= 0) {
            e.preventDefault();
            handleSuggestionClick(allSuggestions[selectedSuggestionIndex]);
        } else if (e.key === "Escape") {
            setShowSuggestions(false);
            setSelectedSuggestionIndex(-1);
        }
    };

    const allSuggestions = [...recentSearches, ...suggestions];
    const filteredSuggestions = allSuggestions.filter(s => 
        s.toLowerCase().includes(query.toLowerCase()) && s !== query
    );

    return (
        <div className={cn("relative", className)}>
            <div className="flex gap-2">
                <div className="relative flex-1">
                    <div className="relative">
                        <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />
                        
                        <Input
                            type="text"
                            value={query}
                            onChange={(e) => {
                                setQuery(e.target.value);
                                setShowSuggestions(true);
                                setSelectedSuggestionIndex(-1);
                            }}
                            onFocus={() => setShowSuggestions(true)}
                            onBlur={() => {
                                // Delay to allow clicking on suggestions
                                setTimeout(() => setShowSuggestions(false), 200);
                            }}
                            onKeyDown={handleKeyDown}
                            placeholder={placeholder}
                            className="pl-10 pr-10"
                            autoFocus={autoFocus}
                        />

                        {query && (
                            <button
                                onClick={handleClear}
                                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors"
                            >
                                <XMarkIcon className="h-4 w-4" />
                            </button>
                        )}
                    </div>

                    {/* Suggestions Dropdown */}
                    {showSuggestions && filteredSuggestions.length > 0 && (
                        <div className="absolute z-10 w-full mt-1 bg-background border border-gray-200 rounded-lg shadow-lg overflow-hidden">
                            {recentSearches.length > 0 && filteredSuggestions.some(s => recentSearches.includes(s)) && (
                                <div className="px-3 py-2 bg-secondary/50 border-b">
                                    <div className="flex items-center text-xs text-muted-foreground">
                                        <ClockIcon className="h-3 w-3 mr-1" />
                                        Recherches récentes
                                    </div>
                                </div>
                            )}
                            
                            <div className="max-h-64 overflow-y-auto">
                                {filteredSuggestions.map((suggestion, index) => {
                                    const isRecent = recentSearches.includes(suggestion);
                                    return (
                                        <button
                                            key={index}
                                            onClick={() => handleSuggestionClick(suggestion)}
                                            className={cn(
                                                "w-full px-4 py-2.5 text-left hover:bg-secondary/50 transition-colors flex items-center",
                                                index === selectedSuggestionIndex && "bg-secondary/50"
                                            )}
                                        >
                                            {isRecent ? (
                                                <ClockIcon className="h-4 w-4 mr-3 text-muted-foreground flex-shrink-0" />
                                            ) : (
                                                <MagnifyingGlassIcon className="h-4 w-4 mr-3 text-muted-foreground flex-shrink-0" />
                                            )}
                                            <span className="text-sm text-foreground truncate">
                                                {suggestion}
                                            </span>
                                        </button>
                                    );
                                })}
                            </div>
                        </div>
                    )}
                </div>

                {showFilters && (
                    <Button
                        variant="outline"
                        size="md"
                        onClick={onFilterClick}
                        className="relative"
                    >
                        <FunnelIcon className="h-4 w-4" />
                        <span className="ml-2">Filtres</span>
                        {filterCount > 0 && (
                            <Badge
                                variant="primary"
                                size="sm"
                                className="absolute -top-2 -right-2 h-5 w-5 p-0 flex items-center justify-center"
                            >
                                {filterCount}
                            </Badge>
                        )}
                    </Button>
                )}
            </div>

            {/* Active Search Indicator */}
            {isLoading && (
                <div className="absolute bottom-0 left-0 w-full h-0.5 bg-primary/20">
                    <div className="h-full bg-primary animate-pulse"></div>
                </div>
            )}
        </div>
    );
};

export default SearchBar;