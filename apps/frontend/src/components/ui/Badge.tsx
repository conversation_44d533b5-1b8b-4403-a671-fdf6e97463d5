import React from "react";
import { cn } from "@/utils";

export interface BadgeProps extends React.HTMLAttributes<HTMLDivElement> {
    variant?:
        | "default"
        | "primary"
        | "secondary"
        | "success"
        | "warning"
        | "destructive"
        | "info"
        | "outline";
    size?: "sm" | "md" | "lg";
    children: React.ReactNode;
    icon?: React.ReactNode;
    dot?: boolean;
}

const Badge = React.forwardRef<HTMLDivElement, BadgeProps>(
    (
        { className, variant = "default", size = "md", children, icon, dot = false, ...props },
        ref,
    ) => {
        const baseClasses =
            "inline-flex items-center font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2";

        const variants = {
            default: "bg-primary/10 text-primary border border-primary/20",
            primary: "bg-primary text-primary-foreground",
            secondary: "bg-secondary text-secondary-foreground border border-gray-200",
            success: "bg-success/10 text-success border border-success/20",
            warning: "bg-warning/10 text-warning border border-warning/20",
            destructive: "bg-destructive/10 text-destructive border border-destructive/20",
            info: "bg-info/10 text-info border border-info/20",
            outline: "text-foreground border border-gray-200 bg-transparent",
        };

        const sizes = {
            sm: "px-2 py-0.5 text-xs rounded-full gap-1",
            md: "px-2.5 py-1 text-sm rounded-full gap-1.5",
            lg: "px-3 py-1.5 text-base rounded-xl gap-2",
        };

        return (
            <div
                ref={ref}
                className={cn(baseClasses, variants[variant], sizes[size], className)}
                {...props}
            >
                {dot && (
                    <span
                        className={cn(
                            "w-2 h-2 rounded-full",
                            variant === "success" && "bg-success",
                            variant === "warning" && "bg-warning",
                            variant === "destructive" && "bg-destructive",
                            variant === "info" && "bg-info",
                            variant === "primary" && "bg-primary",
                            variant === "default" && "bg-primary",
                        )}
                    />
                )}
                {icon && icon}
                {children}
            </div>
        );
    },
);

Badge.displayName = "Badge";

export default Badge;
