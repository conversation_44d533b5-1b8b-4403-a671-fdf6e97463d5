import React from "react";
import { cn } from "@/utils";

export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
    variant?:
        | "primary"
        | "secondary"
        | "outline"
        | "ghost"
        | "destructive"
        | "success"
        | "warning"
        | "info";
    size?: "xs" | "sm" | "md" | "lg" | "xl";
    loading?: boolean;
    icon?: React.ReactNode;
    iconPosition?: "left" | "right";
    fullWidth?: boolean;
    children: React.ReactNode;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
    (
        {
            className,
            variant = "primary",
            size = "md",
            loading = false,
            disabled,
            icon,
            iconPosition = "left",
            fullWidth = false,
            children,
            ...props
        },
        ref,
    ) => {
        const baseClasses =
            "inline-flex items-center justify-center rounded-xl font-semibold transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:opacity-50 disabled:pointer-events-none active:scale-[0.98]";

        const variants = {
            primary:
                "bg-primary text-primary-foreground hover:bg-primary/90 shadow-sm hover:shadow-md",
            secondary:
                "bg-secondary text-secondary-foreground hover:bg-secondary/80 border border-gray-200",
            outline:
                "border-2 border-primary text-primary hover:bg-primary hover:text-primary-foreground",
            ghost: "text-foreground hover:bg-accent hover:text-accent-foreground",
            destructive:
                "bg-destructive text-destructive-foreground hover:bg-destructive/90 shadow-sm hover:shadow-md",
            success:
                "bg-success text-success-foreground hover:bg-success/90 shadow-sm hover:shadow-md",
            warning:
                "bg-warning text-warning-foreground hover:bg-warning/90 shadow-sm hover:shadow-md",
            info: "bg-info text-info-foreground hover:bg-info/90 shadow-sm hover:shadow-md",
        };

        const sizes = {
            xs: "h-7 px-2.5 text-xs gap-1.5",
            sm: "h-8 px-3 text-sm gap-2",
            md: "h-10 px-4 text-sm gap-2",
            lg: "h-12 px-6 text-base gap-2.5",
            xl: "h-14 px-8 text-lg gap-3",
        };

        const LoadingSpinner = () => (
            <svg
                className="h-4 w-4 animate-spin"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
            >
                <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                />
                <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                />
            </svg>
        );

        return (
            <button
                className={cn(
                    baseClasses,
                    variants[variant],
                    sizes[size],
                    fullWidth && "w-full",
                    className,
                )}
                ref={ref}
                disabled={disabled || loading}
                {...props}
            >
                {loading ? (
                    <>
                        <LoadingSpinner />
                        {children}
                    </>
                ) : (
                    <>
                        {icon && iconPosition === "left" && icon}
                        {children}
                        {icon && iconPosition === "right" && icon}
                    </>
                )}
            </button>
        );
    },
);

Button.displayName = "Button";

export default Button;
