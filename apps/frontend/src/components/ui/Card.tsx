import React from "react";
import { cn } from "@/utils";

export interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
    children: React.ReactNode;
    variant?: "default" | "elevated" | "bordered" | "ghost";
    padding?: "none" | "sm" | "md" | "lg" | "xl";
    hover?: boolean;
    interactive?: boolean;
}

export interface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {
    children: React.ReactNode;
}

export interface CardTitleProps extends React.HTMLAttributes<HTMLHeadingElement> {
    children: React.ReactNode;
    as?: "h1" | "h2" | "h3" | "h4" | "h5" | "h6";
}

export interface CardDescriptionProps extends React.HTMLAttributes<HTMLParagraphElement> {
    children: React.ReactNode;
}

export interface CardContentProps extends React.HTMLAttributes<HTMLDivElement> {
    children: React.ReactNode;
}

export interface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {
    children: React.ReactNode;
}

const Card = React.forwardRef<HTMLDivElement, CardProps>(
    (
        {
            className,
            children,
            variant = "default",
            padding = "md",
            hover = false,
            interactive = false,
            ...props
        },
        ref,
    ) => {
        const baseClasses = "rounded-xl bg-background text-foreground transition-all duration-200";

        const variants = {
            default: "border border-gray-200 shadow-sm",
            elevated: "border border-gray-200 shadow-lg",
            bordered: "border-2 border-gray-200",
            ghost: "border border-transparent",
        };

        const paddings = {
            none: "",
            sm: "p-3",
            md: "p-6",
            lg: "p-8",
            xl: "p-10",
        };

        const hoverStyles = hover
            ? "hover:shadow-lg hover:border-primary/20 hover:-translate-y-1"
            : "";
        const interactiveStyles = interactive ? "cursor-pointer civic-interactive" : "";

        return (
            <div
                ref={ref}
                className={cn(
                    baseClasses,
                    variants[variant],
                    paddings[padding],
                    hoverStyles,
                    interactiveStyles,
                    className,
                )}
                {...props}
            >
                {children}
            </div>
        );
    },
);
Card.displayName = "Card";

const CardHeader = React.forwardRef<HTMLDivElement, CardHeaderProps>(
    ({ className, children, ...props }, ref) => (
        <div ref={ref} className={cn("flex flex-col space-y-1.5 p-6", className)} {...props}>
            {children}
        </div>
    ),
);
CardHeader.displayName = "CardHeader";

const CardTitle = React.forwardRef<HTMLHeadingElement, CardTitleProps>(
    ({ className, children, as: Component = "h3", ...props }, ref) => (
        <Component
            ref={ref}
            className={cn(
                "text-xl font-bold leading-none tracking-tight text-foreground",
                className,
            )}
            {...props}
        >
            {children}
        </Component>
    ),
);
CardTitle.displayName = "CardTitle";

const CardDescription = React.forwardRef<HTMLParagraphElement, CardDescriptionProps>(
    ({ className, children, ...props }, ref) => (
        <p ref={ref} className={cn("text-sm text-muted-foreground", className)} {...props}>
            {children}
        </p>
    ),
);
CardDescription.displayName = "CardDescription";

const CardContent = React.forwardRef<HTMLDivElement, CardContentProps>(
    ({ className, children, ...props }, ref) => (
        <div ref={ref} className={cn("p-6 pt-0", className)} {...props}>
            {children}
        </div>
    ),
);
CardContent.displayName = "CardContent";

const CardFooter = React.forwardRef<HTMLDivElement, CardFooterProps>(
    ({ className, children, ...props }, ref) => (
        <div ref={ref} className={cn("flex items-center p-6 pt-0", className)} {...props}>
            {children}
        </div>
    ),
);
CardFooter.displayName = "CardFooter";

export { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };
