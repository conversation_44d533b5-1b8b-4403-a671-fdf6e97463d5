"use client";

import React from "react";
import { cn } from "@/utils";

interface SkeletonProps {
    className?: string;
    variant?: "default" | "rounded" | "circular";
    width?: string | number;
    height?: string | number;
    count?: number;
    animate?: boolean;
}

const Skeleton: React.FC<SkeletonProps> = ({
    className,
    variant = "default",
    width,
    height,
    count = 1,
    animate = true,
}) => {
    const baseClasses = "bg-secondary/50";

    const variantClasses = {
        default: "rounded-md",
        rounded: "rounded-lg",
        circular: "rounded-full",
    };

    const animationClasses = animate ? "animate-pulse" : "";

    const skeletonStyle: React.CSSProperties = {
        width: typeof width === "number" ? `${width}px` : width,
        height: typeof height === "number" ? `${height}px` : height,
    };

    const SkeletonElement = () => (
        <div
            className={cn(baseClasses, variantClasses[variant], animationClasses, className)}
            style={skeletonStyle}
        />
    );

    if (count === 1) {
        return <SkeletonElement />;
    }

    return (
        <>
            {Array.from({ length: count }, (_, index) => (
                <SkeletonElement key={index} />
            ))}
        </>
    );
};

// Preset skeleton components for common use cases
export const SkeletonText: React.FC<{
    lines?: number;
    className?: string;
    width?: string[];
}> = ({ lines = 3, className, width }) => (
    <div className={cn("space-y-2", className)}>
        {Array.from({ length: lines }, (_, index) => (
            <Skeleton
                key={index}
                className="h-4"
                width={width?.[index] || (index === lines - 1 ? "75%" : "100%")}
            />
        ))}
    </div>
);

export const SkeletonCard: React.FC<{
    className?: string;
    showImage?: boolean;
    showHeader?: boolean;
    textLines?: number;
}> = ({ className, showImage = true, showHeader = true, textLines = 3 }) => (
    <div className={cn("p-6 space-y-4", className)}>
        {showImage && <Skeleton className="w-full h-48" variant="rounded" />}
        {showHeader && (
            <div className="space-y-2">
                <Skeleton className="h-6 w-3/4" />
                <Skeleton className="h-4 w-1/2" />
            </div>
        )}
        <SkeletonText lines={textLines} />
    </div>
);

export const SkeletonAvatar: React.FC<{
    size?: "sm" | "md" | "lg" | "xl";
    className?: string;
}> = ({ size = "md", className }) => {
    const sizeClasses = {
        sm: "w-8 h-8",
        md: "w-12 h-12",
        lg: "w-16 h-16",
        xl: "w-20 h-20",
    };

    return <Skeleton variant="circular" className={cn(sizeClasses[size], className)} />;
};

export const SkeletonButton: React.FC<{
    variant?: "default" | "wide" | "icon";
    className?: string;
}> = ({ variant = "default", className }) => {
    const variantClasses = {
        default: "h-10 w-24",
        wide: "h-10 w-32",
        icon: "h-10 w-10",
    };

    return <Skeleton variant="rounded" className={cn(variantClasses[variant], className)} />;
};

export const SkeletonTable: React.FC<{
    rows?: number;
    columns?: number;
    className?: string;
}> = ({ rows = 5, columns = 4, className }) => (
    <div className={cn("space-y-3", className)}>
        {/* Header */}
        <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
            {Array.from({ length: columns }, (_, index) => (
                <Skeleton key={`header-${index}`} className="h-4 w-full" />
            ))}
        </div>

        {/* Rows */}
        {Array.from({ length: rows }, (_, rowIndex) => (
            <div
                key={`row-${rowIndex}`}
                className="grid gap-4"
                style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}
            >
                {Array.from({ length: columns }, (_, colIndex) => (
                    <Skeleton
                        key={`cell-${rowIndex}-${colIndex}`}
                        className="h-4"
                        width={colIndex === 0 ? "60%" : "100%"}
                    />
                ))}
            </div>
        ))}
    </div>
);

export const SkeletonList: React.FC<{
    items?: number;
    showAvatar?: boolean;
    className?: string;
}> = ({ items = 5, showAvatar = true, className }) => (
    <div className={cn("space-y-4", className)}>
        {Array.from({ length: items }, (_, index) => (
            <div key={index} className="flex items-center gap-4">
                {showAvatar && <SkeletonAvatar size="md" />}
                <div className="flex-1 space-y-2">
                    <Skeleton className="h-4 w-3/4" />
                    <Skeleton className="h-3 w-1/2" />
                </div>
                <SkeletonButton variant="default" />
            </div>
        ))}
    </div>
);

export const SkeletonStats: React.FC<{
    items?: number;
    className?: string;
}> = ({ items = 4, className }) => (
    <div className={cn("grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6", className)}>
        {Array.from({ length: items }, (_, index) => (
            <div key={index} className="p-6 bg-background rounded-lg border">
                <div className="flex items-center justify-between mb-4">
                    <div className="space-y-2">
                        <Skeleton className="h-4 w-20" />
                        <Skeleton className="h-8 w-16" />
                    </div>
                    <Skeleton variant="circular" className="w-12 h-12" />
                </div>
                <Skeleton className="h-3 w-24" />
            </div>
        ))}
    </div>
);

export const SkeletonForm: React.FC<{
    fields?: number;
    className?: string;
}> = ({ fields = 5, className }) => (
    <div className={cn("space-y-6", className)}>
        {Array.from({ length: fields }, (_, index) => (
            <div key={index} className="space-y-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-10 w-full" variant="rounded" />
            </div>
        ))}
        <div className="flex gap-4 pt-4">
            <SkeletonButton variant="default" />
            <SkeletonButton variant="wide" />
        </div>
    </div>
);

export default Skeleton;
