"use client";

import { useState, useCallback } from "react";
import { apiClient } from "@/lib/api";

export interface UploadedFile {
    id: string;
    file: File;
    preview?: string;
    url?: string;
    type: "image" | "document" | "video";
    uploaded: boolean;
    error?: string;
    progress?: number;
}

interface UseFileUploadOptions {
    maxFiles?: number;
    maxSize?: number; // in MB
    acceptedTypes?: string[];
    onProgress?: (fileId: string, progress: number) => void;
    onError?: (fileId: string, error: string) => void;
    onSuccess?: (fileId: string, url: string) => void;
}

export const useFileUpload = (options: UseFileUploadOptions = {}) => {
    const {
        maxFiles = 5,
        maxSize = 10,
        acceptedTypes = ["image/*", "application/pdf", ".doc", ".docx"],
        onProgress,
        onError,
        onSuccess,
    } = options;

    const [files, setFiles] = useState<UploadedFile[]>([]);
    const [isUploading, setIsUploading] = useState(false);

    const generateFileId = () => Math.random().toString(36).substr(2, 9);

    const getFileType = (file: File): "image" | "document" | "video" => {
        if (file.type.startsWith("image/")) return "image";
        if (file.type.startsWith("video/")) return "video";
        return "document";
    };

    const createPreview = (file: File): Promise<string | undefined> => {
        return new Promise((resolve) => {
            if (file.type.startsWith("image/")) {
                const reader = new FileReader();
                reader.onload = () => resolve(reader.result as string);
                reader.onerror = () => resolve(undefined);
                reader.readAsDataURL(file);
            } else {
                resolve(undefined);
            }
        });
    };

    const validateFile = (file: File): string | null => {
        // Check file size
        if (file.size > maxSize * 1024 * 1024) {
            return `Le fichier est trop volumineux (max ${maxSize}MB)`;
        }

        // Check file type
        const isAccepted = acceptedTypes.some((type) => {
            if (type.includes("*")) {
                return file.type.startsWith(type.replace("*", ""));
            }
            return file.type === type || file.name.toLowerCase().endsWith(type);
        });

        if (!isAccepted) {
            return `Type de fichier non supporté`;
        }

        return null;
    };

    const uploadToServer = async (file: File, fileId: string): Promise<string> => {
        const formData = new FormData();
        formData.append("files", file);
        formData.append("ref", "api::expression.expression");
        formData.append("refId", "temp"); // Will be updated when expression is created
        formData.append("field", "medias");

        try {
            const response = await apiClient.post("/upload", formData, {
                headers: {
                    "Content-Type": "multipart/form-data",
                },
                onUploadProgress: (progressEvent) => {
                    if (progressEvent.total) {
                        const progress = Math.round(
                            (progressEvent.loaded * 100) / progressEvent.total,
                        );
                        onProgress?.(fileId, progress);

                        setFiles((prev) =>
                            prev.map((f) => (f.id === fileId ? { ...f, progress } : f)),
                        );
                    }
                },
            });

            if (response.data && (response.data as any)[0]) {
                return (response.data as any)[0].url;
            }

            throw new Error("Upload failed: No URL returned");
        } catch (error) {
            console.error("Upload error:", error);
            throw error;
        }
    };

    const addFiles = useCallback(
        async (newFiles: File[]) => {
            // Check if adding these files would exceed maxFiles
            if (files.length + newFiles.length > maxFiles) {
                throw new Error(`Vous ne pouvez télécharger que ${maxFiles} fichiers maximum.`);
            }

            // Validate and create file objects
            const validatedFiles: UploadedFile[] = [];

            for (const file of newFiles) {
                const validation = validateFile(file);
                if (validation) {
                    onError?.("validation", validation);
                    continue;
                }

                const fileId = generateFileId();
                const preview = await createPreview(file);

                validatedFiles.push({
                    id: fileId,
                    file,
                    preview,
                    type: getFileType(file),
                    uploaded: false,
                    progress: 0,
                });
            }

            if (validatedFiles.length === 0) {
                return;
            }

            // Add files to state
            const updatedFiles = [...files, ...validatedFiles];
            setFiles(updatedFiles);

            // Start uploading
            setIsUploading(true);

            try {
                const uploadPromises = validatedFiles.map(async (fileObj) => {
                    try {
                        const url = await uploadToServer(fileObj.file, fileObj.id);

                        const uploadedFile = {
                            ...fileObj,
                            uploaded: true,
                            url,
                            progress: 100,
                        };

                        setFiles((prev) =>
                            prev.map((f) => (f.id === fileObj.id ? uploadedFile : f)),
                        );

                        onSuccess?.(fileObj.id, url);
                        return uploadedFile;
                    } catch (error) {
                        const errorMessage =
                            error instanceof Error ? error.message : "Upload failed";

                        const failedFile = {
                            ...fileObj,
                            uploaded: false,
                            error: errorMessage,
                            progress: 0,
                        };

                        setFiles((prev) => prev.map((f) => (f.id === fileObj.id ? failedFile : f)));

                        onError?.(fileObj.id, errorMessage);
                        return failedFile;
                    }
                });

                await Promise.all(uploadPromises);
            } finally {
                setIsUploading(false);
            }
        },
        [files, maxFiles, onProgress, onError, onSuccess],
    );

    const removeFile = useCallback((fileId: string) => {
        setFiles((prev) => prev.filter((f) => f.id !== fileId));
    }, []);

    const retryUpload = useCallback(
        async (fileId: string) => {
            const fileObj = files.find((f) => f.id === fileId);
            if (!fileObj || fileObj.uploaded) return;

            setIsUploading(true);

            try {
                const url = await uploadToServer(fileObj.file, fileId);

                const uploadedFile = {
                    ...fileObj,
                    uploaded: true,
                    url,
                    error: undefined,
                    progress: 100,
                };

                setFiles((prev) => prev.map((f) => (f.id === fileId ? uploadedFile : f)));

                onSuccess?.(fileId, url);
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : "Upload failed";

                setFiles((prev) =>
                    prev.map((f) =>
                        f.id === fileId ? { ...f, error: errorMessage, progress: 0 } : f,
                    ),
                );

                onError?.(fileId, errorMessage);
            } finally {
                setIsUploading(false);
            }
        },
        [files, onError, onSuccess],
    );

    const clearFiles = useCallback(() => {
        setFiles([]);
    }, []);

    const getUploadedUrls = useCallback(() => {
        return files.filter((f) => f.uploaded && f.url).map((f) => f.url!);
    }, [files]);

    const hasErrors = files.some((f) => f.error);
    const allUploaded = files.length > 0 && files.every((f) => f.uploaded);
    const uploadProgress =
        files.length > 0 ? files.reduce((acc, f) => acc + (f.progress || 0), 0) / files.length : 0;

    return {
        files,
        addFiles,
        removeFile,
        retryUpload,
        clearFiles,
        getUploadedUrls,
        isUploading,
        hasErrors,
        allUploaded,
        uploadProgress,
        canAddMore: files.length < maxFiles,
    };
};

export default useFileUpload;
