"use client";

import { useState, useEffect, useCallback } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { FilterOptions } from "@/components/filters/AdvancedFilters";

interface UseSearchOptions {
    defaultQuery?: string;
    defaultFilters?: FilterOptions;
    onSearch?: (query: string, filters: FilterOptions) => void;
    debounceMs?: number;
    saveToLocalStorage?: boolean;
    storageKey?: string;
}

interface SearchHistory {
    queries: string[];
    filters: FilterOptions[];
}

export const useSearch = (options: UseSearchOptions = {}) => {
    const {
        defaultQuery = "",
        defaultFilters = {},
        onSearch,
        debounceMs = 300,
        saveToLocalStorage = true,
        storageKey = "pillarscan_search_history",
    } = options;

    const router = useRouter();
    const searchParams = useSearchParams();

    const [query, setQuery] = useState(defaultQuery);
    const [filters, setFilters] = useState<FilterOptions>(defaultFilters);
    const [isLoading, setIsLoading] = useState(false);
    const [searchHistory, setSearchHistory] = useState<SearchHistory>({
        queries: [],
        filters: [],
    });

    // Initialize from URL params
    useEffect(() => {
        const urlQuery = searchParams?.get("q") || "";
        const urlFilters = searchParams?.get("filters");

        if (urlQuery) {
            setQuery(urlQuery);
        }

        if (urlFilters) {
            try {
                const parsedFilters = JSON.parse(decodeURIComponent(urlFilters));
                setFilters(parsedFilters);
            } catch (error) {
                console.warn("Invalid filters in URL:", error);
            }
        }
    }, [searchParams]);

    // Load search history from localStorage
    useEffect(() => {
        if (saveToLocalStorage && typeof window !== "undefined") {
            try {
                const stored = localStorage.getItem(storageKey);
                if (stored) {
                    const history = JSON.parse(stored);
                    setSearchHistory(history);
                }
            } catch (error) {
                console.warn("Failed to load search history:", error);
            }
        }
    }, [saveToLocalStorage, storageKey]);

    // Save search history to localStorage
    const saveSearchHistory = useCallback(
        (newHistory: SearchHistory) => {
            if (saveToLocalStorage && typeof window !== "undefined") {
                try {
                    localStorage.setItem(storageKey, JSON.stringify(newHistory));
                    setSearchHistory(newHistory);
                } catch (error) {
                    console.warn("Failed to save search history:", error);
                }
            }
        },
        [saveToLocalStorage, storageKey],
    );

    // Debounced search function
    const debouncedSearch = useCallback(
        debounce((searchQuery: string, searchFilters: FilterOptions) => {
            setIsLoading(true);

            // Update URL
            const params = new URLSearchParams(window.location.search);

            if (searchQuery) {
                params.set("q", searchQuery);
            } else {
                params.delete("q");
            }

            const hasActiveFilters = Object.values(searchFilters).some((value) => {
                if (Array.isArray(value)) return value.length > 0;
                return value !== undefined && value !== null && value !== "";
            });

            if (hasActiveFilters) {
                params.set("filters", encodeURIComponent(JSON.stringify(searchFilters)));
            } else {
                params.delete("filters");
            }

            router.push(`?${params.toString()}`);

            // Save to history - use functional update to avoid dependency on searchHistory
            if (searchQuery.trim()) {
                setSearchHistory((prevHistory) => {
                    const newQueries = [
                        searchQuery,
                        ...prevHistory.queries.filter((q) => q !== searchQuery),
                    ].slice(0, 10); // Keep only last 10

                    const newHistory = {
                        ...prevHistory,
                        queries: newQueries,
                    };

                    // Save to localStorage
                    if (saveToLocalStorage && typeof window !== "undefined") {
                        try {
                            localStorage.setItem(storageKey, JSON.stringify(newHistory));
                        } catch (error) {
                            console.warn("Failed to save search history:", error);
                        }
                    }

                    return newHistory;
                });
            }

            // Call external search handler
            onSearch?.(searchQuery, searchFilters);

            setTimeout(() => setIsLoading(false), 100);
        }, debounceMs),
        [router, onSearch, debounceMs, saveToLocalStorage, storageKey],
    );

    // Trigger search when query or filters change
    useEffect(() => {
        debouncedSearch(query, filters);
    }, [query, filters, debouncedSearch]);

    const updateQuery = useCallback((newQuery: string) => {
        setQuery(newQuery);
    }, []);

    const updateFilters = useCallback((newFilters: FilterOptions) => {
        setFilters(newFilters);
    }, []);

    const clearSearch = useCallback(() => {
        setQuery("");
        setFilters(defaultFilters);

        // Clear URL params
        const params = new URLSearchParams(window.location.search);
        params.delete("q");
        params.delete("filters");
        router.push(`?${params.toString()}`);
    }, [router, defaultFilters]);

    const clearHistory = useCallback(() => {
        const emptyHistory: SearchHistory = { queries: [], filters: [] };
        saveSearchHistory(emptyHistory);
    }, [saveSearchHistory]);

    const removeFromHistory = useCallback(
        (queryToRemove: string) => {
            const newQueries = searchHistory.queries.filter((q) => q !== queryToRemove);
            saveSearchHistory({
                ...searchHistory,
                queries: newQueries,
            });
        },
        [searchHistory, saveSearchHistory],
    );

    const getActiveFilterCount = useCallback(() => {
        let count = 0;
        Object.values(filters).forEach((value) => {
            if (Array.isArray(value)) {
                count += value.length;
            } else if (value !== undefined && value !== null && value !== "") {
                count += 1;
            }
        });
        return count;
    }, [filters]);

    const hasActiveSearch = query.trim() !== "" || getActiveFilterCount() > 0;

    return {
        query,
        filters,
        isLoading,
        searchHistory: searchHistory.queries,
        updateQuery,
        updateFilters,
        clearSearch,
        clearHistory,
        removeFromHistory,
        getActiveFilterCount,
        hasActiveSearch,
    };
};

// Debounce utility function
function debounce<T extends (...args: any[]) => any>(
    func: T,
    wait: number,
): (...args: Parameters<T>) => void {
    let timeout: NodeJS.Timeout;
    return function executedFunction(...args: Parameters<T>) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

export default useSearch;
