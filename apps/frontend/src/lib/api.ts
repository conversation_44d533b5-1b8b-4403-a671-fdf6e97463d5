import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from "axios";
import { ApiResponse, ApiError } from "@/types";

// API Configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:1337/api";

// Create axios instance
const api: AxiosInstance = axios.create({
    baseURL: API_BASE_URL,
    timeout: 10000,
    headers: {
        "Content-Type": "application/json",
    },
});

// Token management
let authToken: string | null = null;

export const setAuthToken = (token: string | null) => {
    authToken = token;
    if (token) {
        api.defaults.headers.common["Authorization"] = `Bearer ${token}`;
        localStorage.setItem("auth_token", token);
    } else {
        delete api.defaults.headers.common["Authorization"];
        localStorage.removeItem("auth_token");
    }
};

export const getAuthToken = (): string | null => {
    if (authToken) return authToken;
    if (typeof window !== "undefined") {
        authToken = localStorage.getItem("auth_token");
        if (authToken) {
            api.defaults.headers.common["Authorization"] = `Bearer ${authToken}`;
        }
    }
    return authToken;
};

// Initialize token on client side
if (typeof window !== "undefined") {
    getAuthToken();
}

// Request interceptor
api.interceptors.request.use(
    (config) => {
        // Add timestamp to prevent caching
        if (config.method === "get") {
            config.params = {
                ...config.params,
                _t: Date.now(),
            };
        }
        return config;
    },
    (error) => {
        return Promise.reject(error);
    },
);

// Response interceptor
api.interceptors.response.use(
    (response: AxiosResponse) => {
        return response;
    },
    (error) => {
        if (error.response?.status === 401) {
            // Token expired or invalid
            setAuthToken(null);
            if (typeof window !== "undefined") {
                window.location.href = "/auth/login";
            }
        }
        return Promise.reject(error);
    },
);

// Generic API methods
export const apiClient = {
    get: async <T>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> => {
        const response = await api.get(url, config);
        return response.data;
    },

    post: async <T>(
        url: string,
        data?: any,
        config?: AxiosRequestConfig,
    ): Promise<ApiResponse<T>> => {
        const response = await api.post(url, data, config);
        return response.data;
    },

    put: async <T>(
        url: string,
        data?: any,
        config?: AxiosRequestConfig,
    ): Promise<ApiResponse<T>> => {
        const response = await api.put(url, data, config);
        return response.data;
    },

    delete: async <T>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> => {
        const response = await api.delete(url, config);
        return response.data;
    },

    patch: async <T>(
        url: string,
        data?: any,
        config?: AxiosRequestConfig,
    ): Promise<ApiResponse<T>> => {
        const response = await api.patch(url, data, config);
        return response.data;
    },
};

// Authentication API
export const authApi = {
    login: async (identifier: string, password: string) => {
        return apiClient.post("/auth/local", { identifier, password });
    },

    register: async (username: string, email: string, password: string) => {
        return apiClient.post("/auth/local/register", { username, email, password });
    },

    forgotPassword: async (email: string) => {
        return apiClient.post("/auth/forgot-password", { email });
    },

    resetPassword: async (code: string, password: string, passwordConfirmation: string) => {
        return apiClient.post("/auth/reset-password", { code, password, passwordConfirmation });
    },

    me: async () => {
        return apiClient.get("/users/me?populate=profile");
    },
};

// Profiles API
export const profilesApi = {
    getMe: () => apiClient.get("/profiles/me"),
    updateMe: (data: any) => apiClient.put("/profiles/me", { data }),
    getStats: (id?: string) => apiClient.get(id ? `/profiles/${id}/stats` : "/profiles/me/stats"),
    getLeaderboard: (params?: any) => apiClient.get("/profiles/leaderboard", { params }),
    updateRole: (id: string, role: string) => apiClient.put(`/profiles/${id}/role`, { role }),
};

// Expressions API
export const expressionsApi = {
    getAll: (params?: any) => apiClient.get("/expressions", { params }),
    getPublic: (params?: any) => apiClient.get("/expressions/public", { params }),
    getById: (id: string) => apiClient.get<any>(`/expressions/${id}`),
    create: (data: any) => apiClient.post("/expressions", { data }),
    update: (id: string, data: any) => apiClient.put(`/expressions/${id}`, { data }),
    delete: (id: string) => apiClient.delete(`/expressions/${id}`),
    submit: (id: string) => apiClient.post(`/expressions/${id}/submit`),
    validate: (id: string, decision: string, data?: any) =>
        apiClient.post(`/expressions/${id}/validate`, { decision, ...data }),
    getModerationQueue: (params?: any) =>
        apiClient.get("/expressions/moderation/queue", { params }),
};

// Pillars API
export const piliersApi = {
    getAll: (params?: any) => apiClient.get("/piliers", { params }),
    getById: (id: string) => apiClient.get(`/piliers/${id}`),
    getStats: (id: string, params?: any) => apiClient.get(`/piliers/${id}/stats`, { params }),
    getGeoDistribution: (id: string, params?: any) =>
        apiClient.get(`/piliers/${id}/geo-distribution`, { params }),
    getTrendingTopics: (id: string, params?: any) =>
        apiClient.get(`/piliers/${id}/trending-topics`, { params }),
};

// Sub-pillars API
export const sousPiliersApi = {
    getAll: (params?: any) => apiClient.get("/sous-piliers", { params }),
    getById: (id: string) => apiClient.get(`/sous-piliers/${id}`),
};

// Locations API
export const lieuxApi = {
    getAll: (params?: any) => apiClient.get("/lieux", { params }),
    getById: (id: string) => apiClient.get(`/lieux/${id}`),
    search: (params: any) => apiClient.get("/lieux/search", { params }),
    getHierarchy: (id: string) => apiClient.get(`/lieux/${id}/hierarchy`),
    getByLevel: (params: any) => apiClient.get("/lieux/by-level", { params }),
    getExpressionStats: (id: string, params?: any) =>
        apiClient.get(`/lieux/${id}/expression-stats`, { params }),
};

// Entities API
export const entitesApi = {
    getAll: (params?: any) => apiClient.get("/entites", { params }),
    getById: (id: string) => apiClient.get(`/entites/${id}`),
    create: (data: any) => apiClient.post("/entites", { data }),
    update: (id: string, data: any) => apiClient.put(`/entites/${id}`, { data }),
    delete: (id: string) => apiClient.delete(`/entites/${id}`),
};

// Validators API
export const validateursApi = {
    getAll: (params?: any) => apiClient.get("/validateurs", { params }),
    getById: (id: string) => apiClient.get(`/validateurs/${id}`),
    create: (data: any) => apiClient.post("/validateurs", { data }),
    update: (id: string, data: any) => apiClient.put(`/validateurs/${id}`, { data }),
};

// Actions API
export const actionsApi = {
    getAll: (params?: any) => apiClient.get("/actions", { params }),
    getById: (id: string) => apiClient.get(`/actions/${id}`),
    create: (data: any) => apiClient.post("/actions", { data }),
    update: (id: string, data: any) => apiClient.put(`/actions/${id}`, { data }),
    delete: (id: string) => apiClient.delete(`/actions/${id}`),
};

// File upload utility
export const uploadFile = async (file: File, folder?: string): Promise<any> => {
    const formData = new FormData();
    formData.append("files", file);
    if (folder) {
        formData.append("path", folder);
    }

    const response = await api.post("/upload", formData, {
        headers: {
            "Content-Type": "multipart/form-data",
        },
    });

    return response.data;
};

// Error handler utility
export const handleApiError = (error: any): ApiError => {
    if (error.response?.data?.error) {
        return error.response.data;
    }

    return {
        error: {
            status: error.response?.status || 500,
            name: "UnknownError",
            message: error.message || "Une erreur inattendue s'est produite",
            details: error.response?.data,
        },
    };
};

export default api;
