import { create } from "zustand";
import { persist } from "zustand/middleware";
import { User, Profile } from "@/types";
import { authApi, setAuthToken } from "@/lib/api";

interface AuthState {
    user: User | null;
    profile: Profile | null;
    token: string | null;
    isAuthenticated: boolean;
    isLoading: boolean;
    error: string | null;
}

interface AuthActions {
    login: (identifier: string, password: string) => Promise<void>;
    register: (username: string, email: string, password: string) => Promise<void>;
    logout: () => void;
    loadUser: () => Promise<void>;
    updateProfile: (data: Partial<Profile>) => void;
    clearError: () => void;
    setLoading: (loading: boolean) => void;
}

type AuthStore = AuthState & AuthActions;

export const useAuthStore = create<AuthStore>()(
    persist(
        (set, get) => ({
            // Initial state
            user: null,
            profile: null,
            token: null,
            isAuthenticated: false,
            isLoading: false,
            error: null,

            // Actions
            login: async (identifier: string, password: string) => {
                try {
                    set({ isLoading: true, error: null });

                    const response = await authApi.login(identifier, password);
                    const { jwt, user } = response.data as any;

                    // Set token
                    setAuthToken(jwt);

                    // Load user profile
                    const userResponse = await authApi.me();
                    const userData = userResponse.data as any;

                    set({
                        user: userData,
                        profile: userData.profile,
                        token: jwt,
                        isAuthenticated: true,
                        isLoading: false,
                        error: null,
                    });
                } catch (error: any) {
                    set({
                        isLoading: false,
                        error: error.response?.data?.error?.message || "Erreur de connexion",
                    });
                    throw error;
                }
            },

            register: async (username: string, email: string, password: string) => {
                try {
                    set({ isLoading: true, error: null });

                    const response = await authApi.register(username, email, password);
                    const { jwt, user } = response.data as any;

                    // Set token
                    setAuthToken(jwt);

                    set({
                        user,
                        profile: user.profile,
                        token: jwt,
                        isAuthenticated: true,
                        isLoading: false,
                        error: null,
                    });
                } catch (error: any) {
                    set({
                        isLoading: false,
                        error: error.response?.data?.error?.message || "Erreur d'inscription",
                    });
                    throw error;
                }
            },

            logout: () => {
                setAuthToken(null);
                set({
                    user: null,
                    profile: null,
                    token: null,
                    isAuthenticated: false,
                    error: null,
                });
            },

            loadUser: async () => {
                try {
                    const token = get().token;
                    if (!token) return;

                    set({ isLoading: true });
                    setAuthToken(token);

                    const response = await authApi.me();
                    const userData = response.data as any;

                    set({
                        user: userData,
                        profile: userData.profile,
                        isAuthenticated: true,
                        isLoading: false,
                    });
                } catch (error) {
                    // Token is invalid, logout
                    get().logout();
                    set({ isLoading: false });
                }
            },

            updateProfile: (data: Partial<Profile>) => {
                const currentProfile = get().profile;
                if (currentProfile) {
                    set({
                        profile: { ...currentProfile, ...data },
                    });
                }
            },

            clearError: () => {
                set({ error: null });
            },

            setLoading: (loading: boolean) => {
                set({ isLoading: loading });
            },
        }),
        {
            name: "auth-storage",
            partialize: (state) => ({
                user: state.user,
                profile: state.profile,
                token: state.token,
                isAuthenticated: state.isAuthenticated,
            }),
        },
    ),
);
