import { create } from "zustand";
import { Expression, ExpressionFilters, <PERSON>lier, Sous<PERSON><PERSON><PERSON>, Lieu } from "@/types";
import { expressions<PERSON>pi, piliers<PERSON>pi, lieuxApi } from "@/lib/api";

interface ExpressionState {
    expressions: Expression[];
    currentExpression: Expression | null;
    pillars: Pilier[];
    locations: Lieu[];
    filters: ExpressionFilters;
    pagination: {
        page: number;
        pageSize: number;
        total: number;
        pageCount: number;
    };
    isLoading: boolean;
    error: string | null;
}

interface ExpressionActions {
    // Expressions
    fetchExpressions: (filters?: ExpressionFilters) => Promise<void>;
    fetchPublicExpressions: (filters?: ExpressionFilters) => Promise<void>;
    fetchExpression: (id: string) => Promise<void>;
    createExpression: (data: any) => Promise<Expression>;
    updateExpression: (id: string, data: any) => Promise<void>;
    deleteExpression: (id: string) => Promise<void>;
    submitExpression: (id: string) => Promise<void>;

    // Pillars and locations
    fetchPillars: () => Promise<void>;
    fetchLocations: (params?: any) => Promise<void>;
    searchLocations: (query: string) => Promise<Lieu[]>;

    // Filters and pagination
    setFilters: (filters: Partial<ExpressionFilters>) => void;
    clearFilters: () => void;
    setPage: (page: number) => void;

    // UI state
    setCurrentExpression: (expression: Expression | null) => void;
    clearError: () => void;
    setLoading: (loading: boolean) => void;
}

type ExpressionStore = ExpressionState & ExpressionActions;

const initialFilters: ExpressionFilters = {
    page: 1,
    pageSize: 25,
};

export const useExpressionStore = create<ExpressionStore>((set, get) => ({
    // Initial state
    expressions: [],
    currentExpression: null,
    pillars: [],
    locations: [],
    filters: initialFilters,
    pagination: {
        page: 1,
        pageSize: 25,
        total: 0,
        pageCount: 0,
    },
    isLoading: false,
    error: null,

    // Actions
    fetchExpressions: async (filters?: ExpressionFilters) => {
        try {
            set({ isLoading: true, error: null });

            const currentFilters = { ...get().filters, ...filters };
            const response = await expressionsApi.getAll(currentFilters);

            set({
                expressions: response.data as Expression[],
                pagination: response.meta?.pagination || get().pagination,
                filters: currentFilters,
                isLoading: false,
            });
        } catch (error: any) {
            set({
                isLoading: false,
                error:
                    error.response?.data?.error?.message ||
                    "Erreur lors du chargement des expressions",
            });
        }
    },

    fetchPublicExpressions: async (filters?: ExpressionFilters) => {
        try {
            set({ isLoading: true, error: null });

            const currentFilters = { ...get().filters, ...filters };
            const response = await expressionsApi.getPublic(currentFilters);

            set({
                expressions: response.data as Expression[],
                pagination: response.meta?.pagination || get().pagination,
                filters: currentFilters,
                isLoading: false,
            });
        } catch (error: any) {
            set({
                isLoading: false,
                error:
                    error.response?.data?.error?.message ||
                    "Erreur lors du chargement des expressions",
            });
        }
    },

    fetchExpression: async (id: string) => {
        try {
            set({ isLoading: true, error: null });

            const response = await expressionsApi.getById(id);

            set({
                currentExpression: response.data,
                isLoading: false,
            });
        } catch (error: any) {
            set({
                isLoading: false,
                error:
                    error.response?.data?.error?.message ||
                    "Erreur lors du chargement de l'expression",
            });
        }
    },

    createExpression: async (data: any) => {
        try {
            set({ isLoading: true, error: null });

            const response = await expressionsApi.create(data);
            const newExpression = response.data as Expression;

            set({
                expressions: [newExpression, ...get().expressions],
                currentExpression: newExpression,
                isLoading: false,
            });

            return newExpression;
        } catch (error: any) {
            set({
                isLoading: false,
                error:
                    error.response?.data?.error?.message ||
                    "Erreur lors de la création de l'expression",
            });
            throw error;
        }
    },

    updateExpression: async (id: string, data: any) => {
        try {
            set({ isLoading: true, error: null });

            const response = await expressionsApi.update(id, data);
            const updatedExpression = response.data;

            set({
                expressions: get().expressions.map((expr) => {
                    return (expr.id === id ? updatedExpression : expr) as Expression;
                }),
                currentExpression: (get().currentExpression?.id === id
                    ? updatedExpression
                    : get().currentExpression) as Expression | null,
                isLoading: false,
            });
        } catch (error: any) {
            set({
                isLoading: false,
                error:
                    error.response?.data?.error?.message ||
                    "Erreur lors de la mise à jour de l'expression",
            });
            throw error;
        }
    },

    deleteExpression: async (id: string) => {
        try {
            set({ isLoading: true, error: null });

            await expressionsApi.delete(id);

            set({
                expressions: get().expressions.filter((expr) => expr.id !== id),
                currentExpression:
                    get().currentExpression?.id === id ? null : get().currentExpression,
                isLoading: false,
            });
        } catch (error: any) {
            set({
                isLoading: false,
                error:
                    error.response?.data?.error?.message ||
                    "Erreur lors de la suppression de l'expression",
            });
            throw error;
        }
    },

    submitExpression: async (id: string) => {
        try {
            set({ isLoading: true, error: null });

            const response = await expressionsApi.submit(id);
            const updatedExpression = response.data;

            set({
                expressions: get().expressions.map(
                    (expr) => (expr.id === id ? updatedExpression : expr) as Expression,
                ),
                currentExpression: (get().currentExpression?.id === id
                    ? updatedExpression
                    : get().currentExpression) as Expression | null,
                isLoading: false,
            });
        } catch (error: any) {
            set({
                isLoading: false,
                error:
                    error.response?.data?.error?.message ||
                    "Erreur lors de la soumission de l'expression",
            });
            throw error;
        }
    },

    fetchPillars: async () => {
        try {
            const response = await piliersApi.getAll({ populate: "sous_piliers" });
            set({ pillars: response.data as Pilier[] });
        } catch (error: any) {
            console.error("Error fetching pillars:", error);
        }
    },

    fetchLocations: async (params?: any) => {
        try {
            const response = await lieuxApi.getAll(params);
            set({ locations: response.data as Lieu[] });
        } catch (error: any) {
            console.error("Error fetching locations:", error);
        }
    },

    searchLocations: async (query: string) => {
        try {
            const response = await lieuxApi.search({ q: query });
            return response.data as Lieu[];
        } catch (error: any) {
            console.error("Error searching locations:", error);
            return [];
        }
    },

    setFilters: (filters: Partial<ExpressionFilters>) => {
        set({
            filters: { ...get().filters, ...filters, page: 1 }, // Reset to page 1 when filters change
        });
    },

    clearFilters: () => {
        set({ filters: initialFilters });
    },

    setPage: (page: number) => {
        set({
            filters: { ...get().filters, page },
        });
    },

    setCurrentExpression: (expression: Expression | null) => {
        set({ currentExpression: expression });
    },

    clearError: () => {
        set({ error: null });
    },

    setLoading: (loading: boolean) => {
        set({ isLoading: loading });
    },
}));
