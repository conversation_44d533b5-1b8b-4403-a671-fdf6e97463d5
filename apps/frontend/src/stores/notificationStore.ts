import { create } from "zustand";

export interface Notification {
    id: string;
    title: string;
    message: string;
    type: "info" | "success" | "warning" | "error";
    timestamp: Date;
    read: boolean;
    actionUrl?: string;
    actionLabel?: string;
    expressionId?: string;
    userId?: string;
}

interface NotificationState {
    notifications: Notification[];
    unreadCount: number;
    isConnected: boolean;
    addNotification: (notification: Omit<Notification, "id" | "timestamp" | "read">) => void;
    markAsRead: (id: string) => void;
    markAllAsRead: () => void;
    removeNotification: (id: string) => void;
    clearAll: () => void;
    setConnectionStatus: (connected: boolean) => void;
}

export const useNotificationStore = create<NotificationState>((set, get) => ({
    notifications: [],
    unreadCount: 0,
    isConnected: false,

    addNotification: (notificationData) => {
        const notification: Notification = {
            id: Math.random().toString(36).substr(2, 9),
            timestamp: new Date(),
            read: false,
            ...notificationData,
        };

        set((state) => ({
            notifications: [notification, ...state.notifications].slice(0, 50), // Keep only last 50
            unreadCount: state.unreadCount + 1,
        }));

        // Auto-remove success notifications after 5 seconds
        if (notification.type === "success") {
            setTimeout(() => {
                get().removeNotification(notification.id);
            }, 5000);
        }
    },

    markAsRead: (id) => {
        set((state) => ({
            notifications: state.notifications.map((notification) =>
                notification.id === id ? { ...notification, read: true } : notification,
            ),
            unreadCount: Math.max(0, state.unreadCount - 1),
        }));
    },

    markAllAsRead: () => {
        set((state) => ({
            notifications: state.notifications.map((notification) => ({
                ...notification,
                read: true,
            })),
            unreadCount: 0,
        }));
    },

    removeNotification: (id) => {
        set((state) => {
            const notification = state.notifications.find((n) => n.id === id);
            const wasUnread = notification && !notification.read;

            return {
                notifications: state.notifications.filter((notification) => notification.id !== id),
                unreadCount: wasUnread ? Math.max(0, state.unreadCount - 1) : state.unreadCount,
            };
        });
    },

    clearAll: () => {
        set({
            notifications: [],
            unreadCount: 0,
        });
    },

    setConnectionStatus: (connected) => {
        set({ isConnected: connected });
    },
}));
